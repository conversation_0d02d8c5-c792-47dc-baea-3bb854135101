// modal-forms.js - Templates for modal forms
import { state } from '../state.js';
import { setState } from '../state.js';
import { renderTemplates } from '../templates.js';
import { modal } from '../ui/modal.js';

const API_BASE_URL = 'https://pbg-backend-445732206884.us-central1.run.app';

export const categoryFormTemplate = `
  <div class="cat-form">
    <h2 class="cat-form__title">Create New Template Category</h2>
    <form id="categoryForm">
      <div class="cat-form__group">
        <label class="cat-form__label" for="categoryName">Category Name</label>
        <input type="text" id="categoryName" name="categoryName" class="cat-form__input" 
          placeholder="e.g., Software Development Proposal" required>
      </div>
      <div class="cat-form__group">
        <label class="cat-form__label" for="categoryDesc">Description</label>
        <textarea id="categoryDesc" name="categoryDesc" class="cat-form__input cat-form__textarea" 
          placeholder="Describe what this category is for..." required></textarea>
      </div>
      <div class="cat-form__actions">
        <button type="submit" class="cat-form__submit">Create Category</button>
        <button type="button" class="cat-form__cancel">Cancel</button>
      </div>
    </form>
  </div>
`;

export const agentFormTemplate = `
  <form id="agentForm" class="form">
    <div class="form-group">
      <label class="form-label" for="agentName">Agent Name</label>
      <input type="text" id="agentName" name="agentName" class="form-input" required 
        placeholder="e.g., Infrastructure Assessment">
    </div>
    <div class="form-group">
      <label class="form-label" for="agentDescription">Description</label>
      <input type="text" id="agentDescription" name="agentDescription" class="form-input" required
        placeholder="e.g., Evaluates current infrastructure and requirements">
    </div>
    <div class="form-group">
      <label class="form-label" for="agentPrompt">AI Prompt</label>
      <textarea id="agentPrompt" name="agentPrompt" class="form-textarea" rows="10" required
        placeholder="# Agent Name&#10;&#10;Enter your AI prompt here...&#10;&#10;## Instructions:&#10;- Be specific about requirements&#10;- Use clear formatting&#10;- Include examples if needed"></textarea>
    </div>
    <div class="form-group">
      <div class="form-checkbox">
        <input type="checkbox" id="agentActive" name="agentActive" checked>
        <label for="agentActive">Active</label>
      </div>
      <div class="form-checkbox">
        <input type="checkbox" id="agentQuestionMode" name="agentQuestionMode">
        <label for="agentQuestionMode">Question Mode</label>
      </div>
    </div>
  </form>
`;

export const proposalFormTemplate = `
  <form id="proposalForm" class="form">
    <div class="form-group">
      <label class="form-label" for="proposalTitle">Proposal Title</label>
      <input type="text" id="proposalTitle" name="proposalTitle" class="form-input" required 
        placeholder="e.g., Cloud Infrastructure Migration">
    </div>
    <div class="form-group">
      <label class="form-label" for="proposalCategory">Category</label>
      <select id="proposalCategory" name="proposalCategory" class="form-select" required>
        <option value="">Select a category</option>
      </select>
      <div id="categoryInfo" class="selected-category-info" style="display: none;"></div>
    </div>
    <div class="form-group">
      <label class="form-label" for="proposalDate">Due Date</label>
      <input type="date" id="proposalDate" name="proposalDate" class="form-input" required>
    </div>
    <div class="form-group">
      <label class="form-label">Upload Documents</label>
      <div class="dropzone" id="proposalDocs">
        <div class="dropzone__prompt">
          <i class="fa fa-cloud-upload"></i>
          <p>Drag & drop files here or click to upload</p>
        </div>
        <input type="file" class="dropzone__input" name="proposalFiles" multiple accept=".pdf,.doc,.docx,.rtf,.md,.csv,.html,.htm">
        <div class="dropzone__preview"></div>
      </div>
    </div>
  </form>
`;

export function populateCategorySelect(selectElement) {
  // Clear existing options except the placeholder
  while (selectElement.options.length > 1) {
    selectElement.remove(1);
  }

  // Add options for each category
  state.templateCategories.forEach(category => {
    const option = document.createElement('option');
    option.value = category.id;
    option.textContent = category.name;
    option.dataset.description = category.description;
    option.dataset.agentCount = Array.isArray(category.agents) ? category.agents.filter(a => a.isActive).length : 0;
    selectElement.appendChild(option);
  });

  // Add change event listener
  selectElement.addEventListener('change', (e) => {
    const categoryInfo = document.getElementById('categoryInfo');
    const selectedCategory = state.templateCategories.find(cat => cat.id === e.target.value);
    
    if (selectedCategory) {
      categoryInfo.innerHTML = `
        <div class="selected-category-info__name">${selectedCategory.name}</div>
      `;
      categoryInfo.style.display = 'block';
    } else {
      categoryInfo.style.display = 'none';
    }
  });
}

export function initDropzone(dropzoneEl) {
  const input = dropzoneEl.querySelector('.dropzone__input');
  const preview = dropzoneEl.querySelector('.dropzone__preview');
  const prompt = dropzoneEl.querySelector('.dropzone__prompt');
  
  function handleFiles(newFiles) {
    // Merge new files with existing ones, avoiding duplicates by name
    let existingFiles = Array.from(dropzoneEl.droppedFiles || []);
    let filesToAdd = Array.from(newFiles);
    // Filter out files with duplicate names
    filesToAdd = filesToAdd.filter(f => !existingFiles.some(ef => ef.name === f.name));
    const mergedFiles = existingFiles.concat(filesToAdd);
    dropzoneEl.droppedFiles = mergedFiles;
    preview.innerHTML = mergedFiles.map(file => `
      <div class="dropzone__file">
        <i class="fa fa-file"></i>
        <span>${file.name}</span>
        <button type="button" class="dropzone__remove" aria-label="Remove file">
          <i class="fa fa-times"></i>
        </button>
      </div>
    `).join('');
    if (mergedFiles.length > 0) {
      prompt.style.display = 'none';
    } else {
      prompt.style.display = 'flex';
    }
  }
  
  // Handle drag & drop
  dropzoneEl.addEventListener('dragover', (e) => {
    e.preventDefault();
    dropzoneEl.classList.add('dropzone--active');
  });
  
  dropzoneEl.addEventListener('dragleave', () => {
    dropzoneEl.classList.remove('dropzone--active');
  });
  
  dropzoneEl.addEventListener('drop', (e) => {
    e.preventDefault();
    dropzoneEl.classList.remove('dropzone--active');
    handleFiles(e.dataTransfer.files);
  });
  
  // Handle click upload
  dropzoneEl.addEventListener('click', () => {
    input.click();
  });
  
  input.addEventListener('change', () => {
    handleFiles(input.files);
  });
  
  // Handle remove
  preview.addEventListener('click', (e) => {
    const removeBtn = e.target.closest('.dropzone__remove');
    if (removeBtn) {
      const fileEl = removeBtn.closest('.dropzone__file');
      const name = fileEl.querySelector('span').textContent;
      if (dropzoneEl.droppedFiles) {
        dropzoneEl.droppedFiles = Array.from(dropzoneEl.droppedFiles).filter(f => f.name !== name);
      }
      // Re-render the preview with the updated file list
      const files = Array.from(dropzoneEl.droppedFiles || []);
      preview.innerHTML = files.map(file => `
        <div class="dropzone__file">
          <i class="fa fa-file"></i>
          <span>${file.name}</span>
          <button type="button" class="dropzone__remove" aria-label="Remove file">
            <i class="fa fa-times"></i>
          </button>
        </div>
      `).join('');
      if (files.length === 0) {
        prompt.style.display = 'flex';
        dropzoneEl.droppedFiles = undefined;
      }
    }
  });
}

// Add template via API
export async function addTemplate(templateData) {
  const response = await fetch(`${API_BASE_URL}/api/templates`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(templateData)
  });
  // Fix: Only parse JSON if response has content
  const text = await response.text();
  if (!text) return null;
  try {
    return JSON.parse(text);
  } catch (e) {
    console.error('Failed to parse JSON:', e, text);
    return null;
  }
}

// Add custom agent via API
export async function addAgent(templateId, agentData) {
  console.log(agentData)
  const response = await fetch(`${API_BASE_URL}/api/templates/${templateId}/agents`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(agentData)
  });
  if (!response.ok) {
    throw new Error(`Failed to add agent: ${response.status} ${response.statusText}`);
  }
  const text = await response.text();
  if (!text) return null;
  try {
    return JSON.parse(text);
  } catch (e) {
    console.error('Failed to parse JSON:', e, text);
    return null;
  }
}

if (typeof window !== 'undefined') {
  // Remove form submit for cat-form
  // Handle template creation on button click
  document.addEventListener('click', async (e) => {
    const btn = e.target.closest('.cat-form__submit');
    if (btn) {
      e.preventDefault();
      const form = btn.closest('form');
      if (form) {
        const name = form.querySelector('#categoryName')?.value;
        const description = form.querySelector('#categoryDesc')?.value;
        if (name && description) {
          await addTemplate({ name, description });
          // Fetch updated categories and refresh UI
          const response = await fetch(`${API_BASE_URL}/api/templates`);
          const categories = response.ok ? await response.json() : [];
          setState({ templateCategories: categories });
          renderTemplates();
          // Instead of modal.close(), trigger click on cancel button
          const cancelBtn = document.querySelector('.cat-form__cancel');
          if (cancelBtn) cancelBtn.click();
        }
      }
    }
  });

  // Handle add custom agent button
  document.addEventListener('click', async (e) => {
    const btn = e.target.closest('.modal__submit');
    if (btn) {
      e.preventDefault();
      // Get proposalId from closest .template-detail's data-cat-id
      const templateDetail = document.querySelector('.template-detail');
      const templateId = templateDetail ? templateDetail.getAttribute('data-cat-id') : null;
      const form = document.getElementById('agentForm');
      const name = form?.querySelector('#agentName')?.value;
      const role = form?.querySelector('#agentDescription')?.value;
      const prompt = form?.querySelector('#agentPrompt')?.value;
      if (templateId && name && role && prompt) {
        //await addAgent(templateId, { name, role, prompt });
        // Optionally, close modal or update UI here
      }
    }
  });
} 