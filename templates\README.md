# DOCX Template Setup

## Overview
The DOCX export now uses a template-based approach for better formatting and Word compatibility.

## Setup Instructions

1. **Place your DOCX template file** in this `templates` folder
2. **Name the file**: `proposal-template.docx`
3. **Add placeholder variables** in your template using the format `{variableName}`

## Available Template Variables

### Content Variables
- `{content}` - Rich HTML content from the editor
- `{contentText}` - Plain text version of the content

### Proposal Information
- `{customerName}` - Customer/Client name
- `{proposalName}` - Proposal title
- `{volumeName}` - Volume name
- `{solicitationNumber}` - RFI/RFP solicitation number
- `{submittalDate}` - Submission date

### Company Information (PBG)
- `{companyName}` - PBG
- `{companyAddress}` - 7925 Jones Branch Dr., Ste 2125, McLean, VA 22102
- `{companyWebsite}` - PBGtech.com
- `{companyUEI}` - T52YD1L9GCA8
- `{companyCage}` - 65G46
- `{companyDuns}` - *********
- `{companyMAS}` - GS-35F-706GA
- `{contactName}` - <PERSON><PERSON><PERSON>, CFO
- `{contactPhone}` - ************
- `{contactEmail}` - <EMAIL>

### Customer Information
- `{customerAgency}` - Agency name and office
- `{customerEmail}` - Customer email address

### Legal Text
- `{restrictionText}` - Standard government restriction clause

## Template Creation Tips

1. **Use Word styles** for consistent formatting
2. **Set up headers and footers** as needed
3. **Configure page margins** appropriately
4. **Use `{content}` placeholder** where you want the main content to appear
5. **Test with sample data** before deploying

## Example Template Structure

```
Cover Page:
- Company logo
- {customerName}
- {proposalName}
- {volumeName}
- RFI Response
- {solicitationNumber}
- {submittalDate}

Content Pages:
- Header with company info
- {content} (main content area)
- Footer with restriction text

Company Information Section:
- Submitted To: {customerAgency}, {customerEmail}
- Submitted By: {companyName}, {companyAddress}
- Contact: {contactName}, {contactPhone}, {contactEmail}
```

## Benefits of Template Approach

✅ **True DOCX format** - No more Word compatibility issues
✅ **Professional formatting** - Use Word's native styling
✅ **Consistent branding** - Standardized company templates
✅ **Easy maintenance** - Update template file without code changes
✅ **Rich content support** - HTML content properly converted
