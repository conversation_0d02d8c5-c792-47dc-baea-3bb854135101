// modal.js - Reusable modal system with focus trap and accessibility
const modalTemplate = `
  <div class="modal" role="dialog" aria-modal="true">
    <div class="modal__overlay"></div>
    <div class="modal__container" role="document">
      <header class="modal__header">
        <h2 class="modal__title"></h2>
        <button class="modal__close" aria-label="Close modal">
          <i class="fa fa-times"></i>
        </button>
      </header>
      <main class="modal__content"></main>
      <footer class="modal__footer">
        <button class="btn btn-ghost modal__cancel">Cancel</button>
        <button class="btn btn-accent modal__submit">Save</button>
      </footer>
    </div>
  </div>
`;

class Modal {
  constructor() {
    this.activeModal = null;
    this.closeTimer = null;
    this.focusableElements = [];
    this.firstFocusableElement = null;
    this.lastFocusableElement = null;
    this.previousActiveElement = null;
  }

  open(options) {
    const {
      title,
      content,
      onSubmit,
      onCancel,
      onShow,
      submitText = 'Save',
      cancelText = 'Cancel',
      showFooter = true
    } = options;

    // Create modal element
    const modalEl = document.createElement('div');
    modalEl.innerHTML = modalTemplate;
    this.activeModal = modalEl.firstElementChild;
    document.body.appendChild(this.activeModal);

    // Set content
    this.activeModal.querySelector('.modal__title').textContent = title;
    this.activeModal.querySelector('.modal__content').innerHTML = content;

    // Configure buttons
    const submitBtn = this.activeModal.querySelector('.modal__submit');
    const cancelBtn = this.activeModal.querySelector('.modal__cancel');
    const closeBtn = this.activeModal.querySelector('.modal__close');
    const overlay = this.activeModal.querySelector('.modal__overlay');

    if (showFooter) {
      submitBtn.textContent = submitText;
      cancelBtn.textContent = cancelText;
    } else {
      this.activeModal.querySelector('.modal__footer').style.display = 'none';
    }

    // Store previously focused element
    this.previousActiveElement = document.activeElement;

    // Set up focus trap
    this.setupFocusTrap();

    // Event listeners
    submitBtn.addEventListener('click', () => {
      if (onSubmit) {
        const form = this.activeModal.querySelector('form');
        if (form) {
          if (form.checkValidity()) {
            onSubmit(form);
            this.close();
          } else {
            form.reportValidity();
          }
        } else {
          onSubmit();
          this.close();
        }
      } else {
        this.close();
      }
    });

    const handleCancel = () => {
      if (onCancel) onCancel();
      this.close();
    };

    cancelBtn.addEventListener('click', handleCancel);
    closeBtn.addEventListener('click', handleCancel);
    overlay.addEventListener('click', handleCancel);

    // Handle escape key
    document.addEventListener('keydown', this.handleKeyDown.bind(this));

    // Show modal with animation
    requestAnimationFrame(() => {
      this.activeModal.classList.add('modal--visible');
      // Focus first focusable element
      if (this.firstFocusableElement) {
        this.firstFocusableElement.focus();
      }
      // Call onShow callback after modal is visible
      if (onShow) {
        onShow(this.activeModal);
      }
    });
  }

  close() {
    if (!this.activeModal) return;

    // Prevent multiple close calls
    if (this.closeTimer) {
      clearTimeout(this.closeTimer);
    }
    
    this.activeModal.classList.remove('modal--visible');
    
    // Remove after animation
    this.closeTimer = setTimeout(() => {
      if (this.activeModal) { // Check if modal still exists
        document.body.removeChild(this.activeModal);
        this.activeModal = null;
      }
      
      // Restore focus
      if (this.previousActiveElement) {
        this.previousActiveElement.focus();
      }
      this.closeTimer = null;
    }, 200);

    // Remove event listeners
    document.removeEventListener('keydown', this.handleKeyDown.bind(this));
  }

  handleKeyDown(e) {
    if (!this.activeModal) return;

    if (e.key === 'Escape') {
      this.close();
      return;
    }

    if (e.key === 'Tab') {
      // Handle focus trap
      if (e.shiftKey) {
        if (document.activeElement === this.firstFocusableElement) {
          e.preventDefault();
          this.lastFocusableElement.focus();
        }
      } else {
        if (document.activeElement === this.lastFocusableElement) {
          e.preventDefault();
          this.firstFocusableElement.focus();
        }
      }
    }
  }

  setupFocusTrap() {
    const focusableElements = this.activeModal.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    this.focusableElements = Array.from(focusableElements);
    this.firstFocusableElement = this.focusableElements[0];
    this.lastFocusableElement = this.focusableElements[this.focusableElements.length - 1];
  }
}

// Export a singleton instance
export const modal = new Modal(); 