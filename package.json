{"name": "frontend-server", "version": "1.0.0", "description": "Express server for frontend PDF export and static serving", "main": "server.js", "scripts": {"start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"docx": "^8.5.0", "docxtemplater": "^3.65.2", "ejs": "^3.1.10", "express": "^4.18.2", "express-session": "^1.18.1", "mammoth": "^1.9.1", "node-fetch": "^3.3.2", "passport": "^0.7.0", "passport-azure-ad": "^4.3.5", "passport-google-oauth20": "^2.0.0", "pizzip": "^3.2.0", "puppeteer": "^22.8.2", "showdown": "^2.1.0"}}