const express = require('express');
const puppeteer = require('puppeteer');
const path = require('path');
const fetch = (...args) => import('node-fetch').then(mod => mod.default(...args));
const bodyParser = require('body-parser');
const session = require('express-session');
const passport = require('passport');
const OIDCStrategy = require('passport-azure-ad').OIDCStrategy;
const fs = require('fs');
const ejs = require('ejs');

const app = express();
const PORT = 8080;

// Set EJS as the view engine
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Serve static files from the public folder
app.use(express.static(path.join(__dirname, 'public')));

// Parse JSON bodies
app.use(bodyParser.json({ limit: '5mb' }));

// Session middleware
app.use(session({
  secret: '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', // Change this to a secure secret
  resave: false,
  saveUninitialized: false,
  cookie: { secure: false } // Set to true if using HTTPS
}));

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Passport user serialization
passport.serializeUser((user, done) => {
  done(null, user);
});
passport.deserializeUser((user, done) => {
  done(null, user);
});

// Replace with your Azure AD values
const AZURE_AD_CLIENT_ID = '5035b414-c704-4820-9f5e-7de90bdc11f1';
const AZURE_AD_CLIENT_SECRET = '****************************************';
const AZURE_AD_TENANT_ID = '6269b5cb-9085-4f73-835c-9fe59a1584cd';
// Use production redirect URI for Azure AD
const AZURE_AD_REDIRECT_URI = 'https://pbg-frontend-v1-************.us-central1.run.app/auth/azuread/callback';

passport.use(new OIDCStrategy({
  identityMetadata: `https://login.microsoftonline.com/${AZURE_AD_TENANT_ID}/v2.0/.well-known/openid-configuration`,
  clientID: AZURE_AD_CLIENT_ID,
  responseType: 'code',
  responseMode: 'query',
  redirectUrl: AZURE_AD_REDIRECT_URI,
  clientSecret: AZURE_AD_CLIENT_SECRET,
  scope: ['profile', 'email', 'openid'],
  allowHttpForRedirectUrl: true, // for local dev only
}, (iss, sub, profile, accessToken, refreshToken, done) => {
  console.log('AzureAD profile:', profile);
  const email = (profile._json.preferred_username || profile._json.email || '').toLowerCase();
  if (!profile.oid || !email.endsWith('@pbgtech.com')) {
    return done(null, false, { message: 'Only @pbgtech.com email addresses are allowed.' });
  }
  return done(null, {
    email,
    displayName: profile.displayName
  });
}));

// Middleware to check authentication
function ensureAuthenticated(req, res, next) {
  if (req.isAuthenticated() && req.user && req.user.email.endsWith('@pbgtech.com')) {
    return next();
  }
  else{
    res.redirect('/login');
  }
}

// Login page
app.get('/login', (req, res) => {
  const errorMessage = req.session.errorMessage;
  console.log("err", errorMessage)
  res.render('login', { errorMessage });
  req.session.errorMessage = null; // Clear after rendering
});

// Microsoft OAuth routes
app.get('/auth/azuread',
  passport.authenticate('azuread-openidconnect', { failureRedirect: '/login' })
);

app.get('/auth/azuread/callback',
  passport.authenticate('azuread-openidconnect', { failureRedirect: '/login' }),
  (req, res) => {
    console.log('req.user after AzureAD callback:', req.user);
    if (!req.user || !req.user.email.endsWith('@pbgtech.com')) {
      req.session.errorMessage = 'Only @pbgtech.com email addresses are allowed.';
      req.session.save(() => {
        res.redirect('/login');
      });
      return;
    }
    req.session.errorMessage = null;
    res.redirect('/');
  }
);

// Logout route
app.get('/logout', (req, res) => {
  req.logout(() => {
    res.redirect('/login');
  });
});

// Protect the root route
app.get('/', (req, res) => {
  // Render the main app EJS template
  res.render('index');
});

// Example protected main app route
app.get('/db', ensureAuthenticated, (req, res) => {
  // Render your main app EJS or send your SPA
  res.sendFile(path.join(__dirname, 'db.html'));
  // Or: res.render('dashboard', { user: req.user });
});

// Puppeteer PDF export endpoint
app.get('/api/export-pdf', async (req, res) => {
  const url = req.query.url;
  if (!url) {
    return res.status(400).send('Missing url query parameter');
  }
  try {
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    const page = await browser.newPage();
    await page.goto(url, { waitUntil: 'networkidle0' });

    // No need to wait for .response-editor or inject content; print page is ready

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: { top: '40px', bottom: '40px', left: '32px', right: '32px' },
      displayHeaderFooter: false
    });
    await browser.close();
    res.set('Content-Type', 'application/pdf');
    res.set('Content-Disposition', 'attachment; filename="proposal.pdf"');
    res.send(pdfBuffer);
  } catch (err) {
    res.status(500).send('PDF export failed: ' + err.message);
  }
});

// Print-friendly proposal route
app.get('/print/proposal/:id', async (req, res) => {
  const proposalId = req.params.id;
  try {
    // Fetch proposal data from backend API
    const apiUrl = `https://pbg-backend-************.us-central1.run.app/api/proposals/${proposalId}`;
    const response = await fetch(apiUrl);
    if (!response.ok) throw new Error('Failed to fetch proposal data');
    const proposal = await response.json();
    // Render the print-friendly EJS template
    res.render('print-proposal', { proposal });
  } catch (err) {
    res.status(500).send('Error loading proposal for print: ' + err.message);
  }
});

// PDF export from client-provided HTML
app.post('/api/export-pdf-direct', async (req, res) => {
  const { html, title } = req.body;
  if (!html) return res.status(400).send('Missing html');
  try {
    // Render the print template with provided HTML
    const ejs = require('ejs');
    const fs = require('fs');
    const templatePath = path.join(__dirname, 'views', 'print-proposal-direct.ejs');
    const template = fs.readFileSync(templatePath, 'utf8');
    const renderedHtml = ejs.render(template, { html, title });

    const browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox', '--disable-setuid-sandbox'] });
    const page = await browser.newPage();
    await page.setContent(renderedHtml, { waitUntil: 'networkidle0' });

    // Puppeteer header/footer HTML (inline styles, limited CSS)
    const headerTemplate = `
      <div style="width:100%; box-sizing:border-box; background:#edf8fb; min-height:64px; font-family:'Inter',Arial,sans-serif; margin:0; padding:0; border:none;">
        <div style="display:flex; justify-content:space-between; align-items:center; width:100%; font-size:16px; font-weight:bold; color:#25406D; padding:0 32px; height:48px; background:#edf8fb; box-sizing:border-box;">
          <span style="color:#23A4DE; font-size:22px; font-weight:900;">
            <img src="data:image/png;base64,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" alt="pbg" style="width:64px;height:auto;"/>
          </span>
          <span style="color:#888; font-size:15px; font-weight:600; text-align:right;">
            <span>${(title || 'proposal')}</span>
          </span>
        </div>
        <div style="width:100%; height:8px; background:#e57373; margin:0; padding:0;"></div>
      </div>
    `;
    const footerTemplate = `
      <div style="width:100%; box-sizing:border-box; background:#fff; font-family:'Inter',Arial,sans-serif; margin:0; padding:0; border:none;">
        <div style="width:100%; height:3px; background:#64b5f6; margin:0; padding:0;"></div>
        <div style="display:flex; justify-content:center; align-items:center; width:100%; font-size:11px; color:#444; padding:0 32px; height:28px; box-sizing:border-box;">
          <span style="flex:1; text-align:center; font-size:11px; color:#444;">Use or disclosure of data contained on this sheet is subject to the restriction on the title page of this proposal.</span>
          <span style="flex:0 0 auto; text-align:right; font-size:11px; color:#444; margin-left:8px;">Page <span class=\"pageNumber\"></span></span>
        </div>
      </div>
    `;

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: { top: '0px', bottom: '0px', left: '0px', right: '0px' },
      displayHeaderFooter: true,
      headerTemplate,
      footerTemplate
    });
    await browser.close();
    res.set('Content-Type', 'application/pdf');
    res.set('Content-Disposition', `attachment; filename="${(title || 'proposal')}.pdf"`);
    res.send(pdfBuffer);
  } catch (err) {
    res.status(500).send('PDF export failed: ' + err.message);
  }
});

// DOCX export from client-provided HTML - creates a real DOCX file
app.post('/api/export-docx-direct', async (req, res) => {
  const { html, title } = req.body;
  if (!html) return res.status(400).send('Missing html');

  try {
    const { Document, Packer, Paragraph, TextRun, Header, Footer, AlignmentType, HeadingLevel, PageBreak } = require('docx');

    // Convert HTML to plain text for now (you can enhance this with proper HTML parsing)
    const htmlToText = (html) => {
      return html
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .trim();
    };

    const contentText = htmlToText(html);

    // Create the document
    const doc = new Document({
      sections: [
        {
          // First section - Cover page (no header/footer)
          properties: {
            page: {
              margin: {
                top: 0,
                right: 0,
                bottom: 0,
                left: 0,
              },
            },
          },
          children: [
            // Cover page content
            new Paragraph({
              children: [
                new TextRun({
                  text: "PBG",
                  bold: true,
                  size: 48,
                  color: "23A4DE",
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 400 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "[Customer Name]",
                  bold: true,
                  size: 32,
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 200 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "[Proposal Name]",
                  bold: true,
                  size: 28,
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 200 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "[Volume Name]",
                  bold: true,
                  size: 24,
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 200 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "RFI Response",
                  bold: true,
                  size: 22,
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 100 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "[Solicitation Number]",
                  size: 16,
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 50 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "[Submittal Date]",
                  size: 16,
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 400 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "Submitted To:",
                  bold: true,
                  size: 16,
                }),
              ],
              spacing: { after: 100 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "Customer [Agency Name, Office Name]",
                  size: 14,
                }),
              ],
              spacing: { after: 50 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "Email Address",
                  size: 14,
                }),
              ],
              spacing: { after: 200 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "Submitted By:",
                  bold: true,
                  size: 16,
                }),
              ],
              spacing: { after: 100 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "PBG\n7925 Jones Branch Dr., Ste 2125, McLean, VA 22102 | PBGtech.com\n\nUEI: T52YD1L9GCA8 | CAGE Code: 65G46 | DUNS Number: 964955558\n\nMultiple Award Schedule (MAS): GS-35F-706GA\n\nPBG is an SBA-certified 8(a) and Women-Owned Small Business (WOSB)\n\nPoint of Contact: Ghaleb Ghaleb, CFO | 202.830.8046 | <EMAIL>",
                  size: 14,
                }),
              ],
              spacing: { after: 400 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "This proposal or quotation includes data that shall not be disclosed outside of the Government and shall not be duplicated, used, or disclosed in whole or in part for any purpose other than to evaluate this proposal or quotation. If, however, a contract is awarded to this Offeror as a result of or in connection with the submission of this data, the Government shall have the right to duplicate, use, or disclose the data to the extent provided in the resulting contract. This restriction does not limit the Government's right to use information contained in this data if it is obtained from other sources without restriction. The data subject to this restriction are contained in this entire proposal.",
                  size: 11,
                  italics: true,
                }),
              ],
              alignment: AlignmentType.CENTER,
            }),
            new PageBreak(),
          ],
        },
        {
          // Second section - Content pages (with header/footer)
          properties: {
            page: {
              margin: {
                top: 1440, // 1 inch
                right: 1440,
                bottom: 1440,
                left: 1440,
              },
            },
          },
          headers: {
            default: new Header({
              children: [
                new Paragraph({
                  children: [
                    new TextRun({
                      text: "PBG - Project Name - Volume Number/Name - Solicitation No.: xxxx",
                      size: 20,
                    }),
                  ],
                  alignment: AlignmentType.CENTER,
                }),
              ],
            }),
          },
          footers: {
            default: new Footer({
              children: [
                new Paragraph({
                  children: [
                    new TextRun({
                      text: "Use or disclosure of data contained on this sheet is subject to the restriction on the title page of this proposal.",
                      size: 20,
                    }),
                  ],
                  alignment: AlignmentType.CENTER,
                }),
              ],
            }),
          },
          children: [
            new Paragraph({
              children: [
                new TextRun({
                  text: contentText,
                  size: 22,
                }),
              ],
            }),
          ],
        },
      ],
    });

    // Generate the DOCX buffer
    const buffer = await Packer.toBuffer(doc);

    res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    res.set('Content-Disposition', `attachment; filename="${(title || 'proposal')}.docx"`);
    res.send(buffer);
<!DOCTYPE html>
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40">
<head>
  <meta charset="utf-8">
  <title>${title || 'Proposal'}</title>
  <!--[if gte mso 9]>
  <xml>
    <w:WordDocument>
      <w:View>Print</w:View>
      <w:Zoom>90</w:Zoom>
      <w:DoNotPromptForConvert/>
      <w:DoNotShowInsertionsAndDeletions/>
    </w:WordDocument>
  </xml>
  <![endif]-->
  <style>
    @page {
      size: 8.5in 11in;
      margin: 1in;
      mso-header-margin: 0.5in;
      mso-footer-margin: 0.5in;
      mso-paper-source: 0;
    }
    @page:first {
      margin: 0;
      mso-header: none;
      mso-footer: none;
    }
    @page Section1 {
      margin: 1in;
      mso-header: url("#header") h1;
      mso-footer: url("#footer") f1;
    }
    body {
      font-family: Calibri, Arial, sans-serif;
      font-size: 11pt;
      line-height: 1.15;
      margin: 0;
      mso-pagination: widow-orphan;
    }
    .cover-page {
      page-break-after: always;
      text-align: center;
      padding-top: 2in;
      margin: 0;
      position: relative;
      overflow: hidden;
    }
    .cover-page::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 80px;
      height: 100%;
      background-color: #1e3a5f;
      z-index: 1;
    }
    .cover-logo {
      margin-bottom: 1in;
    }
    .cover-customer {
      font-size: 24pt;
      font-weight: bold;
      margin-bottom: 16pt;
      color: #000;
    }
    .cover-proposal {
      font-size: 20pt;
      font-weight: bold;
      margin-bottom: 16pt;
      color: #000;
    }
    .cover-volume {
      font-size: 18pt;
      font-weight: bold;
      margin-bottom: 16pt;
      color: #000;
    }
    .cover-rfi {
      font-size: 16pt;
      font-weight: bold;
      margin-bottom: 8pt;
      color: #000;
    }
    .cover-solicitation {
      font-size: 12pt;
      margin-bottom: 4pt;
      color: #000;
    }
    .cover-date {
      font-size: 12pt;
      margin-bottom: 32pt;
      color: #000;
    }
    .cover-bar {
      width: 140px;
      height: 8px;
      background-color: #E74C3C;
      margin: 0 auto 32pt auto;
      border: none;
    }
    .cover-submitted {
      width: 80%;
      max-width: 6in;
      margin: 0 auto;
      text-align: left;
      margin-bottom: 120px;
      z-index: 2;
      position: relative;
    }
    .cover-section-title {
      font-size: 12pt;
      font-weight: bold;
      margin-bottom: 8pt;
      margin-top: 16pt;
      color: #000;
    }
    .cover-section-content {
      font-size: 11pt;
      line-height: 1.4;
      margin-bottom: 16pt;
      color: #000;
    }
    .cover-restriction {
      position: absolute;
      bottom: 40px;
      left: 32px;
      right: 112px;
      font-size: 9pt;
      text-align: center;
      line-height: 1.3;
      color: #444;
      z-index: 2;
      max-width: calc(100% - 144px);
    }
    .content {
      page: Section1;
    }
    h1 {
      font-size: 16pt;
      color: #1f4e79;
      font-weight: bold;
      margin-top: 12pt;
      margin-bottom: 6pt;
    }
    h2 {
      font-size: 14pt;
      color: #1f4e79;
      font-weight: bold;
      margin-top: 10pt;
      margin-bottom: 6pt;
    }
    h3 {
      font-size: 12pt;
      color: #1f4e79;
      font-weight: bold;
      margin-top: 8pt;
      margin-bottom: 4pt;
    }
    p {
      margin-top: 0;
      margin-bottom: 6pt;
    }
    ul, ol {
      margin-top: 0;
      margin-bottom: 6pt;
    }
    li {
      margin-bottom: 3pt;
    }
    table {
      border-collapse: collapse;
      width: 100%;
    }
    td, th {
      border: 1px solid #ddd;
      padding: 8pt;
      text-align: left;
    }
    .ql-editor {
      border: none !important;
      box-shadow: none !important;
      padding: 0 !important;
    }
  </style>
</head>
<body>
  <!-- Cover Page -->
  <div class="cover-page">
    <div class="cover-logo">
      <img src="https://storage.googleapis.com/pwa-frontend/images/pbg-logo.png" alt="PBG Logo" width="280" height="auto" />
    </div>

    <div class="cover-customer">[Customer Name]</div>
    <div class="cover-proposal">[Proposal Name]</div>
    <div class="cover-volume">[Volume Name]</div>
    <div class="cover-rfi">RFI Response</div>
    <div class="cover-solicitation">[Solicitation Number]</div>
    <div class="cover-date">[Submittal Date]</div>

    <hr class="cover-bar" />

    <div class="cover-submitted">
      <div class="cover-section-title">Submitted To:</div>
      <div class="cover-section-content">
        Customer [Agency Name, Office Name]<br>
        Email Address
      </div>

      <div class="cover-section-title">Submitted By:</div>
      <div class="cover-section-content">
        PBG<br>
        7925 Jones Branch Dr., Ste 2125, McLean, VA 22102 | PBGtech.com<br><br>
        UEI: T52YD1L9GCA8 | CAGE Code: 65G46 | DUNS Number: 964955558<br><br>
        Multiple Award Schedule (MAS): GS-35F-706GA<br><br>
        PBG is an SBA-certified 8(a) and Women-Owned Small Business (WOSB)<br><br>
        Point of Contact: Ghaleb Ghaleb, CFO | 202.830.8046 | <EMAIL>
      </div>
    </div>

    <div class="cover-restriction">
      This proposal or quotation includes data that shall not be disclosed outside of the Government and shall not be duplicated, used, or disclosed in whole or in part for any purpose other than to evaluate this proposal or quotation. If, however, a contract is awarded to this Offeror as a result of or in connection with the submission of this data, the Government shall have the right to duplicate, use, or disclose the data to the extent provided in the resulting contract. This restriction does not limit the Government's right to use information contained in this data if it is obtained from other sources without restriction. The data subject to this restriction are contained in this entire proposal.
    </div>
  </div>

  <!-- Header for subsequent pages -->
  <div id="header" style="display:none;">
    <p style="margin:0; padding:8pt; border-bottom:1px solid #ddd; font-size:10pt; text-align:center;">
      <img src="https://storage.googleapis.com/pwa-frontend/images/pbg-logo-small.png" alt="PBG" style="height:20px; vertical-align:middle; margin-right:16px;" />
      Project Name - Volume Number/Name - Solicitation No.: xxxx
    </p>
  </div>

  <!-- Footer for subsequent pages -->
  <div id="footer" style="display:none;">
    <p style="margin:0; padding:8pt; border-top:1px solid #ddd; font-size:10pt; text-align:center;">
      Use or disclosure of data contained on this sheet is subject to the restriction on the title page of this proposal. Page <span style="mso-field-code: PAGE ">1</span>
    </p>
  </div>

  <!-- Content -->
  <div class="content">
    ${html}
  </div>
</body>
</html>
    `;

    // Set proper headers for Word document
    res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    res.set('Content-Disposition', `attachment; filename="${(title || 'proposal')}.docx"`);
    res.send(docxHtml);
  } catch (err) {
    res.status(500).send('DOCX export failed: ' + err.message);
  }
});

app.listen(PORT, () => {
  console.log(`Frontend Express server running at http://localhost:${PORT}`);
});