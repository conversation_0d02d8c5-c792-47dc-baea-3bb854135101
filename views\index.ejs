<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Proposal Management SPA</title>
  <!-- Quill stylesheet -->
  <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <link rel="stylesheet" href="/css/styles.css">
  <!-- Google Fonts for Quill font family options -->
  <link href="https://fonts.googleapis.com/css?family=Roboto:400,700|Lato:400,700|Montserrat:400,700|Georgia|Courier+New|Arial|Times+New+Roman|sans-serif|serif|monospace" rel="stylesheet">
</head>
<body>
  <main class="grid">
    <aside id="templatesCol" aria-label="Template Categories">
      <div class="section-header">
        <div class="section-title">
          <i class="fa fa-folder section-icon" aria-hidden="true"></i>
          <span>Template Categories</span>
        </div>
        <div class="section-actions">
          <button class="btn btn-primary" id="addCatBtn">
            <i class="fa fa-plus"></i>
            Add Category
          </button>
        </div>
      </div>
      
      <div class="templates-content">
        <!-- Template categories will be rendered here -->
      </div>
    </aside>
    
    <section id="proposalsCol" aria-label="Proposals">
      <div class="section-header">
        <div class="section-title">
          <span>Proposals</span>
        </div>
        <div class="section-actions">
          <div class="dropdown">
            <button class="btn btn-ghost dropdown-trigger" id="exportAllBtn" aria-haspopup="true" aria-expanded="false" aria-controls="exportDropdown">
              <i class="fa fa-download"></i>
              Export All (0)
              <i class="fa fa-caret-down"></i>
            </button>
            <div class="dropdown-content" id="exportDropdown" role="menu" aria-labelledby="exportAllBtn">
              <a href="#" role="menuitem" id="exportCSV" class="dropdown-item">Export as CSV</a>
              <a href="#" role="menuitem" id="exportPDF" class="dropdown-item">Export as PDF</a>
              <a href="#" role="menuitem" id="exportJSON" class="dropdown-item">Export as JSON</a>
            </div>
          </div>
          <button class="btn btn-accent" id="addProposalBtn">
            <i class="fa fa-plus"></i>
            Add New
          </button>
        </div>
      </div>
      
      <div class="proposals-content">
        <!-- Proposals will be rendered here -->
      </div>
    </section>
  </main>

  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

  <!-- Quill library -->
  <script src="https://cdn.quilljs.com/1.3.6/quill.js"></script>
  <script type="module" src="./js/app.js"></script>
</body>
</html> 