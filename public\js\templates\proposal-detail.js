// proposal-detail.js - Proposal detail view functionality
import { state, setState } from '../state.js';
import { modal } from '../ui/modal.js';
import { handleDeleteProposal } from '../proposals.js';
// import showdown from 'showdown'; // REMOVE this line

const API_BASE_URL = 'https://pbg-backend-445732206884.us-central1.run.app';
// Global variables for tracking state
let selectedResponse = null;
let responseContent = '';
let customPrompt = '';
let showQuestionMode = false;
let quillEditors = {}; // Map of agentId -> Quill instance
let currentAgentId = null;

// --- Polling interval for agent status ---
let statusPollingInterval = null;

// Initialize showdown converter
// const mdConverter = new showdown.Converter(); // REMOVE this line

// Clean up function to reset local state
function cleanupProposalDetail() {
  selectedResponse = null;
  responseContent = '';
  customPrompt = '';
  showQuestionMode = false;
  quillEditors = {}; // Clear all Quill instances on cleanup
}

function handleCreateCustomAgent(proposal) {
  modal.open({
    title: 'Add Custom Agent',
    content: `
      <form id="customAgentForm" class="form">
        <div class="form-group">
          <label for="agentName">Agent Name</label>
          <input type="text" id="agentName" name="agentName" required>
        </div>
        <div class="form-group">
          <label for="agentDescription">Description</label>
          <textarea id="agentDescription" name="agentDescription" required></textarea>
        </div>
        <div class="form-group">
          <label for="agentPrompt">AI Prompt</label>
          <textarea id="agentPrompt" name="agentPrompt" required></textarea>
        </div>
        <div class="form-group">
          <label class="checkbox-label">
            <input type="checkbox" name="questionMode">
            Question(s)
          </label>
        </div>
      </form>
    `,
    onSubmit: async () => {
      const form = document.getElementById('customAgentForm');
      if (form.checkValidity()) {
        const formData = new FormData(form);
        try {
          // Call API to add custom agent
          const response = await fetch(`${API_BASE_URL}/api/proposals/${proposal.id}/custom-agents`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: formData.get('agentName'),
              role: formData.get('agentDescription'),
              prompt: formData.get('agentPrompt'),
              questionMode: formData.get('questionMode') === 'on'
            })
          });
          if (!response.ok) throw new Error('Failed to add custom agent');
          const newAgent = await response.json();
          // Re-fetch proposal details to refresh agent list
          const proposalRes = await fetch(`${API_BASE_URL}/api/proposals/${proposal.id}`);
          if (proposalRes.ok) {
            const updatedProposal = await proposalRes.json();
            setState({ selectedProposal: updatedProposal });
            // Immediately update the agent-list in the UI
            const proposalsCol = document.getElementById('proposalsCol');
            if (proposalsCol) {
              proposalsCol.innerHTML = renderProposalDetail(updatedProposal);
              initProposalDetailEventListeners(updatedProposal);
              // Auto-select the newly added custom agent and trigger generate
              setTimeout(() => {
                // Unselect all agent cards
                document.querySelectorAll('.agent-card').forEach(card => card.classList.remove('agent-card--selected'));
                const agentCard = document.querySelector(`.agent-card[data-agent-id="${newAgent.id}"]`);
                if (agentCard) {
                  agentCard.click(); // Trigger click to select and update UI
                  agentCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                const regenerateBtn = document.getElementById('regenerateResponse');
                if (regenerateBtn) {
                  regenerateBtn.click();
                }
              }, 200);
            }
          }
        } catch (err) {
          alert('Failed to add custom agent.');
        }
        return true; // Close modal
      } else {
        form.reportValidity();
        return false; // Keep modal open
      }
    }
  });
}

function handleRemoveCustomAgent(proposal, agentId) {
  if (confirm('Are you sure you want to remove this custom agent?')) {
    const updatedProposal = {
      ...proposal,
      customAgents: proposal.customAgents?.filter(a => a.id !== agentId) || [],
      agentResponses: proposal.agentResponses.filter(r => r.agentId !== agentId)
    };

    // If the removed agent was selected, select the first remaining one
    if (selectedResponse?.agentId === agentId) {
      selectedResponse = updatedProposal.agentResponses[0] || null;
    }

    setState({ selectedProposal: updatedProposal });
  }
}

// Utility: Join all agent responses with separator
function getAllAgentResponsesHTML(proposal) {
  return proposal.agents.map(agent => agent.response || '').join('<hr>');
}

// Utility: Split editor content into agent responses
function splitEditorContentToResponses(html, numAgents) {
  // Split by <hr> (Quill may use <hr> or <hr/> or <hr />)
  let parts = html.split(/<hr\s*\/?>/i);
  // Ensure we have exactly numAgents parts
  while (parts.length < numAgents) parts.push('');
  if (parts.length > numAgents) parts = parts.slice(0, numAgents);
  return parts.map(part => part.trim());
}

// Add loading overlay HTML to the page (once)
function ensureLoadingOverlay() {
  if (!document.getElementById('pdfLoadingOverlay')) {
    const overlay = document.createElement('div');
    overlay.id = 'pdfLoadingOverlay';
    overlay.style = `
      position: fixed; left: 0; top: 0; width: 100vw; height: 100vh; z-index: 9999;
      background: rgba(255,255,255,0.8); display: flex; align-items: center; justify-content: center; flex-direction: column; font-size: 20px; color: #25406D;`;
    overlay.innerHTML = `
      <div style="margin-bottom: 16px;">
        <svg width="48" height="48" viewBox="0 0 50 50"><circle cx="25" cy="25" r="20" fill="none" stroke="#23A4DE" stroke-width="5" stroke-linecap="round" stroke-dasharray="31.415, 31.415" transform="rotate(72.3241 25 25)"><animateTransform attributeName="transform" type="rotate" from="0 25 25" to="360 25 25" dur="1s" repeatCount="indefinite"/></circle></svg>
      </div>
      <div id="loadingOverlayText">Loading...</div>
    `;
    overlay.style.display = 'none';
    document.body.appendChild(overlay);
  }
}

function showLoadingOverlay(message) {
  ensureLoadingOverlay();
  const overlay = document.getElementById('pdfLoadingOverlay');
  if (overlay) {
    overlay.style.display = 'flex';
    const textDiv = document.getElementById('loadingOverlayText');
    if (textDiv) {
      textDiv.textContent = message || 'Loading...';
    }
  }
}

function hideLoadingOverlay() {
  const overlay = document.getElementById('pdfLoadingOverlay');
  if (overlay) overlay.style.display = 'none';
}

export function renderProposalDetail(proposal) {
  // Reset local state when rendering new proposal
  cleanupProposalDetail();

  // Defensive: ensure agentResponses and uploadedDocuments are always arrays
  const agentResponses = Array.isArray(proposal.agents) ? proposal.agents : [];
  const uploadedDocuments = Array.isArray(proposal.documents) ? proposal.documents : [];

  // Format the date for display
  const createdDate = new Date(proposal.createdAt || proposal.createdDate).toLocaleDateString();
  const confirmedCount = agentResponses.filter(r => r.status === 'confirmed').length;
  const totalAgents = agentResponses.length;
  const templateAgents = agentResponses.filter(a => !a.isCustomAgent).length;
  const customAgents = agentResponses.filter(a => a.isCustomAgent).length;
  const remainingCount = totalAgents - confirmedCount;

  // Calculate agent completion progress
  const completedAgents = agentResponses.filter(a => a.status === 'completed').length;
  const progressPercent = totalAgents > 0 ? Math.round((completedAgents / totalAgents) * 100) : 0;

  // Initialize selectedResponse if not set
  if (!selectedResponse && agentResponses.length > 0) {
    selectedResponse = agentResponses[0];
  }

  // Get template category for agent information
  const templateCategory = state.templateCategories.find(cat => cat.id === proposal.templateCategory);

  // --- AGENT EDITORS HTML ---
  const editorsHTML = agentResponses.map(agent => `
    <div class="response-content" id="responseContent-${agent.id}" style="display: ${selectedResponse && selectedResponse.id === agent.id ? 'block' : 'none'};">
      <div id="responseEditor-${agent.id}" class="response-editor" style="height: 300px;"></div>
    </div>
  `).join('');

  return `
    <div class="proposal-detail">
      <section class="proposal-detail__content">
        <div class="proposal-detail__sidebar">
          <header class="proposal-detail__header">
            <div class="proposal-detail__nav">
              <button class="btn btn-ghost" id="backToDashboard">
                <i class="fa fa-arrow-left"></i>
                Back to Dashboard
              </button>
            </div>
            
            <div class="proposal-detail__title-section">
              <h1 class="proposal-detail__title">${proposal.title}</h1>
              <div class="proposal-detail__meta">
                <span>Created: ${createdDate}</span>
                <span class="proposal-detail__tag">${proposal.category}</span>
                <span>${uploadedDocuments.length} document(s)</span>
                <button class="btn btn-danger" id="deleteProposal">
                  <i class="fa fa-trash"></i>
                </button>
                <div class="dropdown">
                  <button class="btn btn-accent dropdown-trigger" id="exportDocumentBtn">
                    <i class="fa fa-download"></i>
                  </button>
                  <div class="dropdown-content">
                    <a href="#" class="dropdown-item" data-format="pdf" role="menuitem">Export as PDF</a>
                    <a href="#" class="dropdown-item" data-format="docx" role="menuitem">Export as DOCX</a>
                    <a href="#" class="dropdown-item" data-format="txt" role="menuitem">Export as TXT</a>
                  </div>
                </div>
              </div>
            </div>

            <div class="proposal-detail__actions">
              <div class="proposal-detail__progress">
                <div class="progress-bar">
                  <div class="progress-bar__fill" style="width: ${progressPercent}%"></div>
                </div>
                <span>${completedAgents}/${totalAgents} completed (${progressPercent}%)</span>
              </div>
            </div>
          </header>
          <div class="sidebar-header">
            <h2>Agents</h2>
            <button class="btn btn-ghost" id="addCustomAgent">
              <i class="fa fa-plus"></i>
              Add Custom
            </button>
          </div>
          <div class="agent-list">
            ${agentResponses.map((agent, index) => {
              const isFirst = index === 0;
              const isLast = index === agentResponses.length - 1;
              return `
                <div class="agent-card ${getAgentStatusClass(agent.status)} ${selectedResponse?.id === agent.id ? 'agent-card--selected' : ''}" 
                     data-agent-id="${agent.id}" data-agent-index="${index}">
                  <div class="agent-card__icon">
                    ${getAgentStatusIcon(agent.status)}
                  </div>
                  <div class="agent-card__content">
                    <div class="agent-card__name">
                      ${agent.name}
                      ${agent.isCustom ? '<small class="agent-card__custom-tag">[Custom]</small>' : ''}
                      <button class="btn btn-ghost btn-xs edit-agent" title="Edit Agent" data-agent-id="${agent.id}" style="margin-left: 0.5em;">
                        <i class="fa fa-edit"></i>
                      </button>
                    </div>
                    <div class="agent-card__status">
                      <span class="agent-status" data-agent-id="${agent.id}">${formatAgentStatus(agent.status)}</span>
                    </div>
                    ${agent.isCustomAgent ? `
                      <button class="btn btn-ghost btn-sm delete-agent" data-agent-id="${agent.agentId}">
                        <i class="fa fa-trash"></i>
                      </button>
                    ` : ''}
                  </div>
                  <div class="agent-card__actions">
                    ${!isFirst ? `<button class="btn btn-ghost btn-xs agent-move-up" title="Move Up" data-agent-id="${agent.id}"><i class="fa fa-chevron-up"></i></button>` : ''}
                    ${!isLast ? `<button class="btn btn-ghost btn-xs agent-move-down" title="Move Down" data-agent-id="${agent.id}"><i class="fa fa-chevron-down"></i></button>` : ''}
                  </div>
                </div>
              `;
            }).join('')}
          </div>
          <section class="proposal-detail__documents">
            <h2>Documents:</h2>
            <div class="document-list">
              ${uploadedDocuments.map(doc => `
                <div class="document-item ${doc.id === proposal.mainDocumentId ? 'document-item--main' : ''}" data-document-id="${doc.id}">
                  <i class="fa fa-file-${getFileIcon(doc.type)}"></i>
                  <span class="document-item__name">${formatDocumentName(doc.name)}</span>
                  <span class="document-item__size">(${formatFileSize(doc.size)})</span>
                  ${doc.id === proposal.mainDocumentId ? '<span class="document-item__badge">MAIN</span>' : ''}
                </div>
              `).join('')}
            </div>
          </section>
        </div>

        <div class="proposal-detail__main">
          <div class="agent-detail">
            <div class="agent-detail__header">
              <h2 id="selectedAgentName">${selectedResponse ? selectedResponse.name : 'Select an agent'}</h2>
              <div class="agent-detail__meta">
                <span id="selectedAgentStatus">${selectedResponse ? `Status: ${formatAgentStatus(selectedResponse.status)}` : ''}</span>
                <span id="selectedAgentModified">${selectedResponse?.lastModified ? `Modified: ${new Date(selectedResponse.lastModified).toLocaleString()}` : ''}</span>
              </div>
              <div class="agent-detail__actions" id="agentDetailActions" style="display: ${selectedResponse?.questionMode ? 'block' : 'none'};">
                <label class="switch">
                  <input type="checkbox" id="questionMode">
                  <span class="slider"></span>
                  <span class="switch__label">Question(s)</span>
                </label>
              </div>
            </div>
            <div id="questionModeSection" class="question-mode-section">
              <h3 class="question-mode-section__title">Question Mode</h3>
              <div class="form-group">
                <label class="question-mode-section__label">What is your current infrastructure setup?</label>
                <textarea class="form-control question-mode-section__input" id="qm-infra" placeholder="Describe your current setup..."></textarea>
              </div>
              <div class="form-group">
                <label class="question-mode-section__label">What are the main challenges you're facing?</label>
                <textarea class="form-control question-mode-section__input" id="qm-challenges" placeholder="List your key challenges..."></textarea>
              </div>
              <button class="btn btn-accent question-mode-section__submit" id="qm-submit">Submit Answers & Regenerate Response</button>
            </div>

            <div class="agent-detail__response">
              <h3>Agent Response</h3>
              ${editorsHTML}
            </div>

            <div class="agent-detail__actions-footer">
              <button class="btn btn-ghost" id="regenerateResponse" ${!selectedResponse ? 'disabled' : ''}>
                <i class="fa fa-refresh"></i>
                Regenerate
              </button>
              <button class="btn btn-accent" id="saveResponse" ${!selectedResponse ? 'disabled' : ''}>
                Save
              </button>
            </div>
          </div>
        </div>
      </section>

      <footer class="proposal-detail__footer">
        <div class="agent-stats">
          <div class="agent-stats__item">
            <span class="agent-stats__label">Total Agents:</span>
            <span class="agent-stats__value">${totalAgents}</span>
          </div>
          <div class="agent-stats__separator"></div>
          <div class="agent-stats__item">
            <span class="agent-stats__label">Template Agents:</span>
            <span class="agent-stats__value">${templateAgents}</span>
          </div>
          <div class="agent-stats__separator"></div>
          <div class="agent-stats__item">
            <span class="agent-stats__label">Custom Agents:</span>
            <span class="agent-stats__value">${customAgents}</span>
          </div>
          <div class="agent-stats__separator"></div>
          <div class="agent-stats__item">
            <span class="agent-stats__label">Confirmed:</span>
            <span class="agent-stats__value">${confirmedCount}</span>
          </div>
          <div class="agent-stats__separator"></div>
          <div class="agent-stats__item">
            <span class="agent-stats__label">Remaining:</span>
            <span class="agent-stats__value">${remainingCount}</span>
          </div>
        </div>
      </footer>
    </div>
  `;
}

export function initProposalDetailEventListeners(proposal) {
  // --- Start polling agent status ---
  function startStatusPolling() {
    // Clear any existing interval
    stopStatusPolling();
    // Poll every 10 seconds
    statusPollingInterval = setInterval(async () => {
      const allCompleted = await checkAgentStatus();
      if (allCompleted) {
        stopStatusPolling();
      }
    }, 10000);
  }
  function stopStatusPolling() {
    if (statusPollingInterval) {
      clearInterval(statusPollingInterval);
      statusPollingInterval = null;
    }
  }

  // Back button
  const backBtn = document.getElementById('backToDashboard');
  if (backBtn) {
    backBtn.addEventListener('click', () => {
      cleanupProposalDetail();
      stopStatusPolling(); // Stop polling when leaving detail view
      setState({ currentView: 'list', selectedProposal: null });
    });
  }

  // Delete button
  const deleteBtn = document.getElementById('deleteProposal');
  if (deleteBtn) {
    deleteBtn.addEventListener('click', () => {
      handleDeleteProposal(proposal.id);
    });
  }

  // Add custom agent button
  const addCustomBtn = document.getElementById('addCustomAgent');
  if (addCustomBtn) {
    addCustomBtn.addEventListener('click', () => handleCreateCustomAgent(proposal));
  }

  // Initialize Quill editors for all agents (always re-init after render)
  proposal.agents.forEach(agent => {
    const editorDiv = document.getElementById(`responseEditor-${agent.id}`);
    if (editorDiv) {
      if (window.Quill) {
        const Font = window.Quill.import('formats/font');
        Font.whitelist = [
          'sans', 'serif', 'monospace', 'roboto', 'lato', 'montserrat', 'georgia', 'courier', 'arial', 'times'
        ];
        window.Quill.register(Font, true);
        const Size = window.Quill.import('attributors/style/size');
        Size.whitelist = ['8px', '10px', '12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px', '36px', '48px', '64px', '72px'];
        window.Quill.register(Size, true);
        // Always create a new Quill instance for this agent
        const quill = new Quill(`#responseEditor-${agent.id}`, {
          theme: 'snow',
          placeholder: 'Agent response will appear here...',
          readOnly: false,
          modules: {
            toolbar: [
              [{ font: Font.whitelist }],
              [{ size: Size.whitelist }],
              ['bold', 'italic', 'underline', 'strike'],
              [{ color: [] }, { background: [] }],
              [{ script: 'sub' }, { script: 'super' }],
              [{ align: [] }],
              [{ list: 'ordered' }, { list: 'bullet' }],
              ['blockquote', 'code-block'],
              ['link'],
              ['clean'],
              ['hr']
            ]
          }
        });
        // Prevent Quill from auto-focusing
        if (quill.root) quill.root.blur();
        // Convert Markdown to HTML before setting content
        let markdownString = agent.response || '';
        markdownString = markdownString.replace(/\\n/g, '\n').replace(/\r\n/g, '\n');
        const html = window.marked.parse(markdownString);
        quill.clipboard.dangerouslyPasteHTML(html);
        quill.on('text-change', () => {
          agent.response = quill.root.innerHTML;
        });
        quillEditors[agent.id] = quill;
      }
    }
  });

  // Show only the selected agent's editor
  function showOnlyEditor(agentId) {
    proposal.agents.forEach(agent => {
      const contentDiv = document.getElementById(`responseContent-${agent.id}`);
      if (contentDiv) {
        contentDiv.style.display = (agent.id === agentId) ? 'block' : 'none';
      }
    });
  }

  // Handle agent selection and actions
  const agentList = document.querySelector('.agent-list');
  if (agentList) {
    agentList.addEventListener('click', (e) => {
      const agentCard = e.target.closest('.agent-card');
      if (agentCard) {
        const agentId = agentCard.dataset.agentId;
        const response = proposal.agents.find(r => r.id === agentId);
        if (response) {
          handleSelectResponse(response, proposal);
          showOnlyEditor(agentId);
        }
      }

      const deleteBtn = e.target.closest('.delete-agent');
      if (deleteBtn) {
        e.stopPropagation();
        const agentId = deleteBtn.dataset.agentId;
        handleRemoveCustomAgent(proposal, agentId);
      }

      // Move up
      const moveUpBtn = e.target.closest('.agent-move-up');
      if (moveUpBtn) {
        e.stopPropagation();
        const agentId = moveUpBtn.dataset.agentId;
        moveAgent(proposal, agentId, -1);
      }
      // Move down
      const moveDownBtn = e.target.closest('.agent-move-down');
      if (moveDownBtn) {
        e.stopPropagation();
        const agentId = moveDownBtn.dataset.agentId;
        moveAgent(proposal, agentId, 1);
      }

      // Edit agent
      const editBtn = e.target.closest('.edit-agent');
      if (editBtn) {
        e.stopPropagation();
        const agentId = editBtn.dataset.agentId;
        const agent = proposal.agents.find(a => a.id === agentId);
        if (agent) {
          handleEditAgent(agent, proposal);
        }
      }
    });
  }

  // Question mode toggle
  const questionModeToggle = document.getElementById('questionMode');
  if (questionModeToggle) {
    questionModeToggle.addEventListener('change', async () => {
      // Show/hide the Question Mode section
      const qmSection = document.getElementById('questionModeSection');
      if (questionModeToggle.checked) {
        // Fetch questions from backend
        if (selectedResponse && proposal && proposal.id && selectedResponse.id) {
          try {
            const res = await fetch(`${API_BASE_URL}/api/proposals/${proposal.id}/agents/${selectedResponse.id}`);

            if (res.ok) {
              const agentData = await res.json();
              // agentData.questions: array of { id, text }
              // agentData.answers: array of { questionId, answer }
              if (Array.isArray(agentData.questions) && agentData.questions.length > 0) {
                // Render questions dynamically
                qmSection.innerHTML = `
                  <h3 class="question-mode-section__title">Question Mode</h3>
                  <form id="qm-form">
                    <div class="question-mode-section__list">
                      ${agentData.questions.map((q, idx) => `
                        <div class="form-group">
                          <label class="question-mode-section__label" for="qm-q${idx}">${q}</label>
                          <textarea class="form-control question-mode-section__input" id="qm-q${idx}" name="qm-q${idx}" placeholder="Enter your answer..."></textarea>
                        </div>
                      `).join('')}
                    </div>
                    <button class="btn btn-accent question-mode-section__submit" id="qm-submit" type="submit">Submit Answers & Regenerate Response</button>
                  </form>
                `;
                qmSection.style.display = 'block';
                // Add submit handler
                const qmForm = document.getElementById('qm-form');
                if (qmForm) {
                  qmForm.addEventListener('submit', async (e) => {
                    e.preventDefault(); 
                    // Collect answers and validate
                    const answers = {};
                    let allFilled = true;
                    agentData.questions.forEach((q, idx) => {
                      const val = qmForm[`qm-q${idx}`].value.trim();
                      if (!val) allFilled = false;
                      answers[`question_${idx + 1}`] = val;
                    });
                    if (!allFilled) {
                      alert('Please answer all questions before submitting.');
                      return;
                    }
                    // Show loading overlay
                    showLoadingOverlay('Analysing answers');
                    try {
                      // Submit answers to backend
                      const res = await fetch(`${API_BASE_URL}/api/proposals/${proposal.id}/agents/${selectedResponse.id}/generate`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ answers })
                      });
                      if (!res.ok) {
                        const err = await res.json().catch(() => ({}));
                        alert('Failed to submit answers: ' + (err.error || res.statusText));
                        hideLoadingOverlay();
                        return;
                      }
                      // After successful submission, fetch updated agent data
                      const agentRes = await fetch(`${API_BASE_URL}/api/proposals/${proposal.id}/agents/${selectedResponse.id}`);
                      if (!agentRes.ok) {
                        alert('Failed to fetch updated agent response.');
                        hideLoadingOverlay();
                        return;
                      }
                      const updatedAgent = await agentRes.json();
                      // Update response editor with new response
                      if (quillEditors[selectedResponse.id]) {
                        let markdownString = updatedAgent.response || '';
                        markdownString = markdownString.replace(/\\n/g, '\n').replace(/\r\n/g, '\n');
                        const html = window.marked.parse(markdownString);
                        quillEditors[selectedResponse.id].clipboard.dangerouslyPasteHTML(html);
                      }
                      // Update agent status in UI
                      selectedResponse.status = updatedAgent.status;
                      const statusSpan = document.querySelector(`.agent-status[data-agent-id="${selectedResponse.id}"]`);
                      if (statusSpan) statusSpan.textContent = formatAgentStatus(updatedAgent.status);
                      updateProposalProgress(proposal);
                      hideLoadingOverlay();
                      alert('Answers submitted and response generated successfully!');
                    } catch (err) {
                      hideLoadingOverlay();
                      alert('Failed to submit answers: ' + err.message);
                    }
                  });
                }
              } else {
                qmSection.style.display = 'none';
              }
            } else {
              qmSection.style.display = 'none';
            }
          } catch (err) {
            qmSection.style.display = 'none';
          }
        } else {
          qmSection.style.display = 'none';
        }
      } else {
        qmSection.style.display = 'none';
      }
    });
  }
  // Show/hide the Question Mode section on initial load
  // Remove:
  // const qmSection = document.getElementById('questionModeSection');
  // if (qmSection && questionModeToggle) {
  //   qmSection.style.display = questionModeToggle.checked ? 'block' : 'none';
  // }
  // Question Mode submit button
  const qmSubmit = document.getElementById('qm-submit');
  if (qmSubmit) {
    qmSubmit.addEventListener('click', () => {
      const infra = document.getElementById('qm-infra').value;
      const challenges = document.getElementById('qm-challenges').value;
      const timeline = document.getElementById('qm-timeline').value;
      alert(`Infra: ${infra}\nChallenges: ${challenges}\nTimeline: ${timeline}`);
      // You can add logic here to use these answers for regeneration or send to backend
    });
  }

  // On initial load, show only the selected agent's editor
  if (selectedResponse) {
    showOnlyEditor(selectedResponse.id);
    handleSelectResponse(selectedResponse, proposal);
  }

  // --- Start polling when view is loaded ---
  startStatusPolling();

  // Save response (only for selected agent)
  const saveBtn = document.getElementById('saveResponse');
  if (saveBtn) {
    saveBtn.addEventListener('click', () => {
      if (!selectedResponse || !quillEditors[selectedResponse.id]) return;
      selectedResponse.response = quillEditors[selectedResponse.id].root.innerHTML;
      selectedResponse.status = selectedResponse.status === 'not-viewed' ? 'viewed' : selectedResponse.status === 'viewed' ? 'edited' : selectedResponse.status;
      selectedResponse.lastModified = new Date().toISOString();
      // Use the agent-specific API endpoint
      fetch(`${API_BASE_URL}/api/proposals/${proposal.id}/agents/${selectedResponse.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: selectedResponse.status,
          response: selectedResponse.response
        })
      })
        .then(async (res) => {
          if (!res.ok) {
            const error = await res.json().catch(() => ({}));
            throw new Error(error.error || 'Failed to save agent response');
          }
          return res.json();
        })
        .then((updatedAgent) => {
          // Update the selected agent in proposal.agents
          const agentIdx = proposal.agents.findIndex(a => a.id === selectedResponse.id);
          if (agentIdx !== -1) {
            proposal.agents[agentIdx] = { ...proposal.agents[agentIdx], ...updatedAgent };
            setState({ selectedProposal: { ...proposal, agents: proposal.agents } });
          }
          if (quillEditors[selectedResponse.id]) {
            let markdownString = updatedAgent.response || '';
            markdownString = markdownString.replace(/\\n/g, '\n').replace(/\r\n/g, '\n');
            quillEditors[selectedResponse.id].clipboard.dangerouslyPasteHTML(window.marked.parse(markdownString));
            alert('Agent response saved successfully');
          }
          // Update agent status and progress UI
          const statusSpan = document.querySelector(`.agent-status[data-agent-id="${selectedResponse.id}"]`);
          if (statusSpan) statusSpan.textContent = formatAgentStatus(updatedAgent.status);
          updateProposalProgress(proposal);
        })
        .catch((err) => {
          alert('Failed to save agent response: ' + err.message);
        });
    });
  }

  // Regenerate response (only for selected agent)
  const regenerateBtn = document.getElementById('regenerateResponse');
  if (regenerateBtn) {
    regenerateBtn.addEventListener('click', async () => {
      if (!selectedResponse) return;

      const questionModeToggle = document.getElementById('questionMode');
      const qmSection = document.getElementById('questionModeSection');

      // If question mode toggle exists and is OFF, turn it ON and prompt user
      if (
        questionModeToggle &&
        !questionModeToggle.checked &&
        selectedResponse &&
        selectedResponse.questionMode
      ) {
        questionModeToggle.checked = true;
        // Trigger the change event to load questions and show the section
        questionModeToggle.dispatchEvent(new Event('change'));
        if (qmSection) qmSection.style.display = 'block';
        alert('Please answer the questions before regenerating the response.');
        return;
      }

      try {
        regenerateBtn.disabled = true;
        regenerateBtn.innerHTML = '<i class="fa fa-refresh"></i> Regenerating...';
        // Set agent status to 'generating' in UI
        selectedResponse.status = 'generating';
        const statusSpan = document.querySelector(`.agent-status[data-agent-id="${selectedResponse.id}"]`);
        if (statusSpan) statusSpan.textContent = formatAgentStatus('generating');
        // Optionally update progress bar
        updateProposalProgress(proposal);

        // --- NEW LOGIC: Check for question mode and collect answers ---
        let requestBody = { context: '' };
        if (
          questionModeToggle &&
          questionModeToggle.checked &&
          qmSection &&
          qmSection.style.display !== 'none'
        ) {
          // Try to find the dynamic question form
          const qmForm = qmSection.querySelector('#qm-form');
          if (qmForm) {
            // Dynamic questions
            const answers = {};
            let allFilled = true;
            const questionLabels = qmForm.querySelectorAll('label.question-mode-section__label');
            questionLabels.forEach((label, idx) => {
              const textarea = qmForm.querySelector(`#qm-q${idx}`);
              if (textarea) {
                const val = textarea.value.trim();
                if (!val) allFilled = false;
                answers[`question_${idx + 1}`] = val;
              }
            });
            if (!allFilled) {
              alert('Please answer all questions before regenerating.');
              regenerateBtn.disabled = false;
              regenerateBtn.innerHTML = '<i class="fa fa-refresh"></i> Regenerate';
              return;
            }
            requestBody = { answers };
          }
        }
        // --- END NEW LOGIC ---

        const res = await fetch(`${API_BASE_URL}/api/proposals/${proposal.id}/agents/${selectedResponse.id}/generate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestBody)
        });
        if (!res.ok) throw new Error('Failed to generate response');
        const data = await res.json();
        selectedResponse.response = data.response;
        // Update status to 'completed' or from backend if provided
        selectedResponse.status = data.status || 'completed';
        if (quillEditors[selectedResponse.id]) {
          let markdownString = data.response;
          markdownString = markdownString.replace(/\\n/g, '\n').replace(/\r\n/g, '\n');
          const html = window.marked.parse(markdownString);
          quillEditors[selectedResponse.id].clipboard.dangerouslyPasteHTML(html);
        }
        // Update agent status in UI
        if (statusSpan) statusSpan.textContent = formatAgentStatus(selectedResponse.status);
        // Update progress bar and text
        updateProposalProgress(proposal);
      } catch (err) {
        alert('Failed to regenerate response.');
        // Optionally reset status
        selectedResponse.status = 'edited';
        const statusSpan = document.querySelector(`.agent-status[data-agent-id="${selectedResponse.id}"]`);
        if (statusSpan) statusSpan.textContent = formatAgentStatus('edited');
        updateProposalProgress(proposal);
      } finally {
        regenerateBtn.disabled = false;
        regenerateBtn.innerHTML = '<i class="fa fa-refresh"></i> Regenerate';
      }
    });
  }

  // Edit AI prompt
  const editPromptBtn = document.getElementById('editAIPrompt');
  if (editPromptBtn) {
    editPromptBtn.addEventListener('click', () => {
      handleEditPrompt(proposal);
    });
  }

  // Update checkAgentStatus to return true if all agents are completed/confirmed
  const checkAgentStatus = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/proposals/${proposal.id}/status`);
      if (response.ok) {
        const statusData = await response.json();
        let allCompleted = true;
        // Track which agents need response refresh
        const agentsToRefresh = [];
        statusData.agents.forEach(agentStatus => {
          const agent = proposal.agents.find(a => a.id === agentStatus.id);
          if (agent) {
            // Only update status here
            agent.status = agentStatus.status;
          }
          const statusSpan = document.querySelector(`.agent-status[data-agent-id="${agentStatus.id}"]`);
          if (statusSpan) {
            statusSpan.textContent = formatAgentStatus(agentStatus.status);
          }
          if (agentStatus.status === 'completed' || agentStatus.status === 'confirmed') {
            // Mark for response refresh
            agentsToRefresh.push(agentStatus.id);
          } else {
            allCompleted = false;
          }
        });
        // If any agent needs a response refresh, fetch the full proposal and update responses
        if (agentsToRefresh.length > 0) {
          const proposalRes = await fetch(`${API_BASE_URL}/api/proposals/${proposal.id}`);
          if (proposalRes.ok) {
            const updatedProposal = await proposalRes.json();
            // Update in-memory agent responses
            updatedProposal.agents.forEach(updatedAgent => {
              const agent = proposal.agents.find(a => a.id === updatedAgent.id);
              if (agent) {
                agent.response = updatedAgent.response;
                // If this is the selected agent, update the editor
                if (selectedResponse && selectedResponse.id === agent.id && quillEditors[agent.id]) {
                  // Only update if questionModeSection is hidden
                  const qmSection = document.getElementById('questionModeSection');
                  if (qmSection && qmSection.style.display !== 'none' && qmSection.offsetParent !== null) {
                    // questionModeSection is visible, skip update
                    return;
                  }
                  if (quillEditors[agent.id].root.innerHTML !== (updatedAgent.response || '')) {
                    let markdownString = updatedAgent.response || '';
                    markdownString = markdownString.replace(/\\n/g, '\n').replace(/\r\n/g, '\n');
                    const html = window.marked.parse(markdownString);
                    const quill = quillEditors[agent.id];
                    if (quill) {
                      // Find the nearest scrollable parent
                      function getScrollableParent(node) {
                        while (node && node !== document.body) {
                          const overflowY = window.getComputedStyle(node).overflowY;
                          if (overflowY === 'auto' || overflowY === 'scroll') return node;
                          node = node.parentElement;
                        }
                        return window;
                      }
                      const scrollParent = getScrollableParent(quill.root);
                      const scrollTop = scrollParent === window ? window.scrollY : scrollParent.scrollTop;
                      // Remove focus from the editor and any element
                      if (document.activeElement) document.activeElement.blur();
                      quill.clipboard.dangerouslyPasteHTML(html);
                      // Remove focus again just in case
                      if (document.activeElement) document.activeElement.blur();
                      // Restore scroll position
                      if (scrollParent === window) {
                        window.scrollTo(window.scrollX, scrollTop);
                      } else {
                        scrollParent.scrollTop = scrollTop;
                      }
                    }
                    selectedResponse.response = updatedAgent.response;
                  }
                }
              }
            });
          }
        }
        // Always update progress bar after updating agents
        updateProposalProgress(proposal);
        return allCompleted;
      }
    } catch (e) {
      console.error('Error checking agent status:', e);
    }
    return false;
  };

  // Check status once when page loads
  checkAgentStatus();

  // Export document dropdown
  const exportDropdown = document.querySelector('.dropdown-content');
  if (exportDropdown) {
    exportDropdown.addEventListener('click', async (e) => {
      e.preventDefault();
      const exportItem = e.target.closest('.dropdown-item');
      if (!exportItem) return;
      const format = exportItem.dataset.format;

      // Stack content from all editors in agent-list order
      const agentOrder = Array.from(document.querySelectorAll('.agent-card')).map(card => card.dataset.agentId);
      const stackedHtml = agentOrder.map(agentId => {
        return quillEditors[agentId] ? quillEditors[agentId].root.innerHTML : '';
      }).join('<hr>');

      let blob, filename;
      if (format === 'txt') {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = stackedHtml;
        const exportText = tempDiv.innerText || tempDiv.textContent || '';
        blob = new Blob([exportText], { type: 'text/plain' });
        filename = `${proposal.title || 'export'}.txt`;
      } else if (format === 'pdf') {
        // Use new backend endpoint for PDF export with current editor HTML
        const exportBtn = exportItem;
        if (exportBtn) exportBtn.textContent = 'Exporting...';
        showLoadingOverlay('Generating PDF, please wait...');
        try {
          // Gather current HTML from all .response-editor editors
          const agentOrder = Array.from(document.querySelectorAll('.agent-card')).map(card => card.dataset.agentId);
          const stackedHtml = agentOrder.map(agentId => {
            return quillEditors[agentId] ? `<div class=\"ql-editor\">${quillEditors[agentId].root.innerHTML}</div>` : '';
          }).join('<hr>');
          const payload = {
            html: stackedHtml,
            title: proposal.title || 'export'
          };
          const response = await fetch('/api/export-pdf-direct', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
          });
          if (!response.ok) throw new Error('Failed to export PDF');
          const blob = await response.blob();
          const filename = `${proposal.title || 'export'}.pdf`;
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          setTimeout(() => {
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
          }, 100);
        } catch (err) {
          alert('Failed to export PDF: ' + err.message);
        } finally {
          if (exportBtn) exportBtn.textContent = 'Export as PDF';
          hideLoadingOverlay();
        }
        return;
      } else if (format === 'docx') {
        // DOCX export with styled static cover, header/footer, and correct page breaks
        const docxHtml = `
          <!DOCTYPE html>
          <html lang="en">
          <head>
            <meta charset="UTF-8">
            <title>${proposal.title || 'Proposal'} - Export</title>
            <style>
              @page {
                margin: 80px 32px 80px 32px;
              }
              @page:first {
                margin: 0;
                header: none;
                footer: none;
              }
              body { font-family: 'Inter', Arial, sans-serif; margin: 0; }
              .cover-page {
                position: relative;
                min-height: 1000px;
                background: #fff;
                padding: 0;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
              }
              .cover-logo {
                margin-top: 80px;
                margin-bottom: 24px;
              }
              .cover-logo img {
                width: 110px;
                height: auto;
              }
              .cover-title {
                text-align: center;
                font-size: 28px;
                font-weight: bold;
                margin-bottom: 8px;
              }
              .cover-customer {
                text-align: center;
                font-size: 22px;
                font-weight: bold;
                margin-bottom: 8px;
              }
              .cover-proposal {
                text-align: center;
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 8px;
              }
              .cover-volume {
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 8px;
              }
              .cover-rfi {
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 8px;
              }
              .cover-solicitation {
                text-align: center;
                font-size: 15px;
                margin-bottom: 2px;
              }
              .cover-date {
                text-align: center;
                font-size: 15px;
                margin-bottom: 32px;
              }
              .cover-bar {
                width: 140px;
                height: 8px;
                background: #E74C3C;
                margin: 40px auto 32px auto;
                border-radius: 2px;
              }
              .cover-submitted {
                margin: 0 auto 0 0;
                font-size: 16px;
                font-weight: bold;
                color: #B71C1C;
                width: 80%;
                max-width: 600px;
                border-top: 0;
                margin-top: 32px;
              }
              .cover-to-by {
                width: 80%;
                max-width: 600px;
                margin: 0 auto;
                font-size: 15px;
                color: #222;
              }
              .cover-to-by .section-title {
                font-weight: bold;
                margin-top: 16px;
              }
              .cover-footer {
                position: absolute;
                left: 0; right: 0; bottom: 24px;
                text-align: center;
                font-size: 11px;
                color: #444;
                padding: 0 32px;
              }
              .content { margin-top: 60px; margin-bottom: 40px; }
              /* Page break after cover */
              .page-break { page-break-after: always; }
            </style>
          </head>
          <body>
            <!-- Cover Page -->
            <div class="cover-page">
              <div class="cover-logo">
                <img src="https://storage.googleapis.com/pwa-frontend/images/pbg-logo.png" alt="PBG Logo" />
              </div>
              <div class="cover-customer">[Customer Name]</div>
              <div class="cover-proposal">[Proposal Name]</div>
              <div class="cover-volume">[Volume Name]</div>
              <div class="cover-rfi">RFI Response</div>
              <div class="cover-solicitation">[Solicitation Number]</div>
              <div class="cover-date">[Submittal Date]</div>
              <div class="cover-bar"></div>
              <div class="cover-to-by">
                <div class="section-title">Submitted To:</div>
                Customer [Agency Name, Office Name]<br>
                Email Address
                <div class="section-title" style="margin-top:24px;">Submitted By:</div>
                PBG<br>
                7925 Jones Branch Dr., Ste 2125, McLean, VA 22102 | PBGtech.com<br><br>
                UEI: T52YD1L9GCA8 | CAGE Code: 65G46 | DUNS Number: 964955558<br><br>
                Multiple Award Schedule (MAS): GS-35F-706GA<br><br>
                PBG is an SBA-certified 8(a) and Women-Owned Small Business (WOSB)<br><br>
                Point of Contact: Ghaleb Ghaleb, CFO | 202.830.8046 | <EMAIL>
              </div>
              <br>
              <br>
              <br>
              <div class="cover-footer">
                This proposal or quotation includes data that shall not be disclosed outside of the Government and shall not be duplicated, used, or disclosed in whole or in part for any purpose other than to evaluate this proposal or quotation. If, however, a contract is awarded to this Offeror as a result of or in connection with the submission of this data, the Government shall have the right to duplicate, use, or disclose the data to the extent provided in the resulting contract. This restriction does not limit the Government's right to use information contained in this data if it is obtained from other sources without restriction. The data subject to this restriction are contained in this entire proposal.
              </div>
            </div>
            <div class="page-break"></div>
            <div class="content">
              ${stackedHtml}
            </div>
          </body>
          </html>
        `;
        blob = new Blob([docxHtml], { type: 'application/msword' });
        filename = `${proposal.title || 'export'}.doc`;
      } else {
        return;
      }
      if (blob) {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        setTimeout(() => {
          document.body.removeChild(link);
          URL.revokeObjectURL(link.href);
        }, 100);
      }
    });
  }
}

function handleSelectResponse(response, proposal) {
  selectedResponse = response;
  responseContent = response.response;
  customPrompt = response.customPrompt || '';
  // Update UI
  const agentName = document.getElementById('selectedAgentName');
  const agentStatus = document.getElementById('selectedAgentStatus');
  const agentModified = document.getElementById('selectedAgentModified');
  // Hide questionModeSection and uncheck questionMode when switching agents
  const qmSection = document.getElementById('questionModeSection');
  const questionModeToggle = document.getElementById('questionMode');

  if (agentName) agentName.textContent = response.name;
  if (agentStatus) agentStatus.textContent = `Status: ${formatAgentStatus(response.status)}`;
  if (agentModified && response.lastModified) {
    agentModified.textContent = `Modified: ${new Date(response.lastModified).toLocaleString()}`;
  }
  // Only show/hide agent-detail__actions based on questionMode
  const agentDetailActions = document.getElementById('agentDetailActions');
  if (agentDetailActions) {
    agentDetailActions.style.display = response.questionMode ? 'block' : 'none';
  }
  document.querySelectorAll('.agent-card').forEach(card => {
    card.classList.toggle('agent-card--selected', card.dataset.agentId === response.id);
  });
  // Always update the Quill editor with the latest response
  if (quillEditors[response.id]) {
    let markdownString = response.response || '';
    markdownString = markdownString.replace(/\\n/g, '\n').replace(/\r\n/g, '\n');
    const html = window.marked.parse(markdownString);
    quillEditors[response.id].clipboard.dangerouslyPasteHTML(html);
  }
  // Auto-enable or disable question mode based on agent status
  if (response.status === "asking_questions") {
    if (questionModeToggle && !questionModeToggle.checked) {
      questionModeToggle.checked = true;
      questionModeToggle.dispatchEvent(new Event('change'));
    }
  } else {
    if (questionModeToggle && questionModeToggle.checked) {
      questionModeToggle.checked = false;
      questionModeToggle.dispatchEvent(new Event('change'));
    }
    if (qmSection) qmSection.style.display = 'none';
  }
  // If questionMode toggle is ON after switching agent, update questions for the new agent
  if (questionModeToggle && questionModeToggle.checked) {
    questionModeToggle.dispatchEvent(new Event('change'));
  }
}

function handleSaveResponse(proposal) {
  if (!selectedResponse) return;

  const updatedResponses = proposal.agents.map(response =>
    response.id === selectedResponse.id
      ? { 
          ...response, 
          response: responseContent,
          customPrompt: customPrompt || undefined,
          status: response.status === 'not-viewed' ? 'viewed' : 
                 response.status === 'viewed' ? 'edited' : response.status,
          lastModified: new Date().toISOString()
        }
      : response
  );

  // Ensure dueDate is in ISO format (YYYY-MM-DDTHH:MM:SSZ)
  let dueDateISO = proposal.dueDate;
  // Always convert to ISO 8601 format (YYYY-MM-DDTHH:MM:SSZ)
  if (dueDateISO instanceof Date) {
    dueDateISO = dueDateISO.toISOString();
  } else if (typeof dueDateISO === 'string') {
    // Try to parse as Date string (e.g., 'Mon, 30 Jun 2025 00:00:00 GMT')
    const parsedDate = new Date(dueDateISO);
    if (!isNaN(parsedDate.getTime())) {
      // If the string parses to a valid date, use its ISO string
      dueDateISO = parsedDate.toISOString();
    } else if (!dueDateISO.includes('T')) {
      // If it's a YYYY-MM-DD string, convert to ISO at midnight UTC
      dueDateISO = new Date(dueDateISO + 'T00:00:00Z').toISOString();
    }
  }

  // Build the payload for the backend (adjust fields as needed)
  const payload = {
    title: proposal.title,
    description: proposal.description,
    category: proposal.category,
    dueDate: dueDateISO,
    agentResponses: updatedResponses
  };

  // Save to backend
  fetch(`${API_BASE_URL}/api/proposals/${proposal.id}` , {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload)
  })
    .then(async (res) => {
      if (!res.ok) {
        const error = await res.json().catch(() => ({}));
        throw new Error(error.error || 'Failed to save proposal');
      }
      return res.json();
    })
    .then((updatedProposal) => {
      selectedResponse = updatedProposal.agentResponses.find(r => r.id === selectedResponse.id);
      setState({ selectedProposal: updatedProposal });
    })
    .catch((err) => {
      alert('Failed to save proposal: ' + err.message);
    });
}

function handleEditPrompt(proposal) {
  if (!selectedResponse) return;

  let currentPrompt = '';
  if (selectedResponse.isCustomAgent) {
    const customAgent = proposal.customAgents?.find(a => a.id === selectedResponse.agentId);
    currentPrompt = customPrompt || customAgent?.prompt || '';
  } else {
    const templateCategory = state.templateCategories.find(cat => cat.id === proposal.templateCategory);
    const agent = templateCategory?.agents.find(a => a.id === selectedResponse.agentId);
    currentPrompt = customPrompt || agent?.prompt || '';
  }

  modal.open({
    title: 'Edit AI Prompt',
    content: `
      <form id="promptForm" class="form">
        <div class="form-group">
          <label for="prompt">AI Prompt</label>
          <textarea id="prompt" name="prompt" required rows="10">${currentPrompt}</textarea>
        </div>
      </form>
    `,
    onSubmit: () => {
      const form = document.getElementById('promptForm');
      if (form.checkValidity()) {
        const formData = new FormData(form);
        customPrompt = formData.get('prompt');
        return true; // Close modal
      }
      return false; // Keep modal open
    }
  });
}

function handleQuestionModeToggle(proposal) {
  if (!selectedResponse) return;

  const questionMode = document.getElementById('questionMode');
  const isInQuestionMode = questionMode.checked;

  const updatedResponses = proposal.agentResponses.map(response =>
    response.id === selectedResponse.id
      ? { ...response, questionMode }
      : response
  );

  const updatedProposal = {
    ...proposal,
    agentResponses: updatedResponses
  };

  selectedResponse = updatedResponses.find(r => r.id === selectedResponse.id);
  setState({ selectedProposal: updatedProposal });
}

function getFileIcon(type) {
  switch (type) {
    case 'main':
      return 'star';
    case 'pdf':
      return 'pdf';
    case 'docx':
      return 'word';
    default:
      return 'text';
  }
}

function formatFileSize(bytes) {
  if (bytes < 1024) return bytes + ' B';
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
  return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
}

function formatDocumentName(name) {
  return name.replace(/^(MAIN|SUPPORT):/, '').trim();
}

function getAgentStatusClass(status) {
  switch (status) {
    case 'confirmed':
      return 'agent-card--confirmed';
    case 'edited':
      return 'agent-card--edited';
    case 'viewed':
      return 'agent-card--viewed';
    case 'generating':
      return 'agent-card--generating';
    default:
      return '';
  }
}

function getAgentStatusIcon(status) {
  switch (status) {
    case 'confirmed':
      return '<i class="fa fa-check-circle"></i>';
    case 'edited':
      return '<i class="fa fa-edit"></i>';
    case 'viewed':
      return '<i class="fa fa-eye"></i>';
    case 'generating':
      return '<i class="fa fa-spinner fa-spin"></i>';
    default:
      return '<i class="fa fa-circle"></i>';
  }
}

function formatAgentStatus(status) {
  return status.charAt(0).toUpperCase() + status.slice(1).replace(/-/g, ' ');
}

// Helper function to move agent
function moveAgent(proposal, agentId, direction) {
  const agents = Array.isArray(proposal.agents) ? [...proposal.agents] : [];
  const index = agents.findIndex(a => a.id === agentId);
  const newIndex = index + direction;
  if (index < 0 || newIndex < 0 || newIndex >= agents.length) return;
  // Swap
  [agents[index], agents[newIndex]] = [agents[newIndex], agents[index]];
  const updatedProposal = { ...proposal, agents };
  setState({ selectedProposal: updatedProposal });
  const proposalsCol = document.getElementById('proposalsCol');
  if (proposalsCol) {
    proposalsCol.innerHTML = renderProposalDetail(updatedProposal);
    initProposalDetailEventListeners(updatedProposal);
    // Refresh Quill content
    if (quillEditors[agentId]) {
      quillEditors[agentId].root.innerHTML = getAllAgentResponsesHTML(updatedProposal);
    }
  }
}

function handleEditAgent(agent, proposal) {
  // Use the modal system to open a form with prefilled values
  modal.open({
    title: 'Edit Agent',
    content: `
      <form id="editAgentForm" class="form">
        <div class="form-group">
          <label for="editAgentName">Agent Name</label>
          <input type="text" id="editAgentName" name="agentName" value="${agent.name}" required>
        </div>
        <div class="form-group">
          <label for="editAgentDescription">Description</label>
          <textarea id="editAgentDescription" name="agentDescription" required>${agent.role || ''}</textarea>
        </div>
        <div class="form-group">
          <label for="editAgentPrompt">AI Prompt</label>
          <textarea id="editAgentPrompt" name="agentPrompt" required>${agent.prompt || ''}</textarea>
        </div>
        <div class="form-group">
          <label class="checkbox-label">
            <input type="checkbox" name="questionMode" ${agent.questionMode ? 'checked' : ''}>
            Question(s)
          </label>
        </div>
      </form>
    `,
    onSubmit: async () => {
      const form = document.getElementById('editAgentForm');
      if (form.checkValidity()) {
        const formData = new FormData(form);
        try {
          // Determine endpoint based on agent type
          const isCustomAgent = agent.isCustomAgent || agent.isCustom;
          const endpoint = isCustomAgent
            ? `${API_BASE_URL}/api/proposals/${proposal.id}/custom-agents/${agent.id}`
            : `${API_BASE_URL}/api/proposals/${proposal.id}/agents/${agent.id}`;
          // Call API to update agent
          const response = await fetch(endpoint, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              isActive: true,
              name: formData.get('agentName'),
              role: formData.get('agentDescription'),
              prompt: formData.get('agentPrompt'),
              questionMode: formData.get('questionMode') === 'on'
            })
          });
          if (!response.ok) throw new Error('Failed to update agent');
          // Optionally, update the agent in the proposal.agents array
          const updatedAgent = await response.json();
          // Re-fetch proposal details to refresh agent list
          const proposalRes = await fetch(`${API_BASE_URL}/api/proposals/${proposal.id}`);
          if (proposalRes.ok) {
            const updatedProposal = await proposalRes.json();
            setState({ selectedProposal: updatedProposal });
            // Immediately update the agent-list in the UI
            const proposalsCol = document.getElementById('proposalsCol');
            if (proposalsCol) {
              proposalsCol.innerHTML = renderProposalDetail(updatedProposal);
              initProposalDetailEventListeners(updatedProposal);
              // After re-render, select the updated agent so the toggle visibility is correct
              const selectedAgent = updatedProposal.agents.find(a => a.id === agent.id);
              if (selectedAgent) {
                handleSelectResponse(selectedAgent, updatedProposal);
                // If question mode is enabled, ensure the question form is rendered before regenerating
                if (selectedAgent.questionMode) {
                  const questionModeToggle = document.getElementById('questionMode');
                  if (questionModeToggle && !questionModeToggle.checked) {
                    questionModeToggle.checked = true;
                    questionModeToggle.dispatchEvent(new Event('change'));
                  }
                  // Wait for #qm-form to appear and be visible, then trigger regenerate
                  let attempts = 0;
                  const maxAttempts = 20; // 2 seconds max
                  const waitForQmForm = () => {
                    const qmSection = document.getElementById('questionModeSection');
                    const qmForm = qmSection && qmSection.querySelector('#qm-form');
                    if (qmForm && qmSection.style.display !== 'none') {
                      const regenerateBtn = document.getElementById('regenerateResponse');
                      if (regenerateBtn) regenerateBtn.click();
                    } else if (attempts < maxAttempts) {
                      attempts++;
                      setTimeout(waitForQmForm, 100);
                    }
                  };
                  waitForQmForm();
                } else {
                  // Not in question mode, trigger regenerate immediately
                  setTimeout(() => {
                    const regenerateBtn = document.getElementById('regenerateResponse');
                    if (regenerateBtn) regenerateBtn.click();
                  }, 200);
                }
              }
              // After re-render, update agent-detail__actions visibility for the selected agent
              const agentDetailActions = document.getElementById('agentDetailActions');
              if (agentDetailActions && selectedAgent) {
                agentDetailActions.style.display = selectedAgent.questionMode ? 'block' : 'none';
              }
            }
          }
        } catch (err) {
          alert('Failed to update agent.');
        }
        return true; // Close modal
      } else {
        form.reportValidity();
        return false; // Keep modal open
      }
    }
  });
}

function exportToPrintablePDF(proposal, quillEditors) {
  // 1. Create a new window
  const printWindow = window.open('', '', 'width=900,height=1200');
  if (!printWindow) return;

  // 2. Build the HTML
  let html = `
    <html>
    <head>
      <title>${proposal.title || 'export'}</title>
      <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
      <style>
        @media print {
          .cover-page { page-break-after: always; }
          .header, .footer { position: fixed; left: 0; right: 0; color: #444; }
          .header { top: 0; }
          .footer { bottom: 0; font-size: 10px; }
          .content { margin-top: 60px; margin-bottom: 40px; }
        }
        .cover-page { text-align: center; margin-top: 100px; }
        .ql-editor { box-shadow: none !important; border: none !important; }
      </style>
    </head>
    <body>
      <div class="cover-page">
        <div style="font-size: 48px; font-weight: bold; margin-bottom: 40px;">LOGO</div>
        <div style="font-size: 32px; font-weight: bold;">${proposal.title || '[Proposal Name]'}</div>
        <div style="margin-top: 40px; font-size: 16px;">
          Submitted To: Customer [Agency Name, Office Name]<br>
          Email Address<br><br>
          Submitted By: PBG<br>
          7925 Jones Branch Dr., Ste 2125, McLean, VA 22102 | PBGtech.com<br>
          UEI: T52YD1L9GCA8 | CAGE Code: 65G46 | DUNS Number: 964955558<br>
          Multiple Award Schedule (MAS): GS-35F-706GA<br>
          PBG is an SBA-certified 8(a) and Women-Owned Small Business (WOSB)<br>
          Point of Contact: Ghaleb Ghaleb, CFO | 202.830.8046 | <EMAIL>
        </div>
      </div>
      <div class="header">PBG - ${proposal.title || 'Project Name'}</div>
      <div class="footer">Use or disclosure of data contained on this sheet is subject to the restriction on the title page of this proposal.</div>
      <div class="content">
  `;

  // 3. Add all agent content
  const agentOrder = Array.from(document.querySelectorAll('.agent-card')).map(card => card.dataset.agentId);
  agentOrder.forEach(agentId => {
    const quill = quillEditors[agentId];
    if (quill) {
      html += `<div class="ql-editor">${quill.root.innerHTML}</div>`;
    }
  });

  html += `
      </div>
      <script>
        window.onload = function() {
          window.print();
        }
      </script>
    </body>
    </html>
  `;

  // 4. Write to the new window and print
  printWindow.document.open();
  printWindow.document.write(html);
  printWindow.document.close();
}

// Helper to update progress bar and text
function updateProposalProgress(proposal) {
  // Defensive: ensure agentResponses is always an array
  const agentResponses = Array.isArray(proposal.agents) ? proposal.agents : [];
  const completedAgents = agentResponses.filter(a => a.status === 'completed' || a.status === 'confirmed').length;
  const totalAgents = agentResponses.length;
  const progressPercent = totalAgents > 0 ? Math.round((completedAgents / totalAgents) * 100) : 0;
  // Update progress bar fill
  const progressFill = document.querySelector('.progress-bar__fill');
  if (progressFill) progressFill.style.width = `${progressPercent}%`;
  // Update progress text
  const progressText = document.querySelector('.proposal-detail__progress span');
  if (progressText) progressText.textContent = `${completedAgents}/${totalAgents} completed (${progressPercent}%)`;
}