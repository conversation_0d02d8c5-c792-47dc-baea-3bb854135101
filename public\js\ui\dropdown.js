// Move these outside the function so they are not redefined on every call
const _dropdownsWithListeners = new WeakSet();

function handleTriggerClick(e) {
  e.preventDefault();
  e.stopPropagation();
  const dropdown = e.currentTarget.closest('.dropdown');
  if (!dropdown) return;
  dropdown.classList.toggle('active');
  const isActive = dropdown.classList.contains('active');
  e.currentTarget.setAttribute('aria-expanded', isActive ? 'true' : 'false');
}

function handleContentClick(e) {
  e.stopPropagation();
  const dropdown = e.currentTarget.closest('.dropdown');
  const trigger = dropdown?.querySelector('.dropdown-trigger');
  const menuItem = e.target.closest('[role="menuitem"]');
  if (menuItem && dropdown && trigger) {
    dropdown.classList.remove('active');
    trigger.setAttribute('aria-expanded', 'false');
  }
}

function handleOutsideClick(e) {
  document.querySelectorAll('.dropdown').forEach(dropdown => {
    if (!dropdown.contains(e.target)) {
      dropdown.classList.remove('active');
      const trigger = dropdown.querySelector('.dropdown-trigger');
      if (trigger) {
        trigger.setAttribute('aria-expanded', 'false');
      }
    }
  });
}

export function initDropdowns() {
  // Remove any existing event listeners for outside click
  document.removeEventListener('click', handleOutsideClick);

  const dropdowns = document.querySelectorAll('.dropdown');

  dropdowns.forEach((dropdown) => {
    const trigger = dropdown.querySelector('.dropdown-trigger');
    const content = dropdown.querySelector('.dropdown-content');
    if (trigger && content && !_dropdownsWithListeners.has(dropdown)) {
      trigger.removeEventListener('click', handleTriggerClick);
      content.removeEventListener('click', handleContentClick);
      trigger.addEventListener('click', handleTriggerClick);
      content.addEventListener('click', handleContentClick);
      trigger.setAttribute('aria-expanded', 'false');
      _dropdownsWithListeners.add(dropdown);
    }
  });

  // Add document click handler
  document.addEventListener('click', handleOutsideClick);
}

// Add error boundary
window.addEventListener('error', (event) => {
  console.error('Error in dropdown.js:', event.error);
}); 