<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title><%= proposal.title %> - Print</title>
  <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
  <link rel="stylesheet" href="/css/styles.css">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    @media print {
      .cover-page { page-break-after: always; }
      .header, .footer { position: fixed; left: 0; right: 0; color: #444; }
      .header { top: 0; }
      .footer { bottom: 0; font-size: 10px; }
      .content { margin-top: 60px; margin-bottom: 40px; }
      .agent-section { page-break-after: always; }
      .agent-section:last-child { page-break-after: auto; }
    }
    body { font-family: 'Inter', <PERSON><PERSON>, sans-serif; margin: 0; }
    .cover-page { text-align: center; margin-top: 100px; }
    .ql-editor {
      box-shadow: none !important;
      border: none !important;
      overflow: visible !important;
      word-break: break-word;
      padding: 16px 0;
      font-size: 16px;
      line-height: 1.6;
    }
    .header { top: 0; text-align: center; font-size: 18px; font-weight: bold; background: #fff; padding: 10px 0; }
    .footer { bottom: 0; text-align: center; background: #fff; padding: 5px 0; }
    .content {
      max-width: 800px;
      margin: 0 auto;
      padding: 40px 32px 40px 32px;
      overflow: visible;
    }
    .agent-section { margin-bottom: 40px; }
    .agent-title { font-size: 20px; font-weight: bold; margin-bottom: 8px; }
    .agent-role { font-size: 14px; color: #666; margin-bottom: 12px; }
    hr { border: none; border-top: 1px solid #ccc; margin: 32px 0; }
  </style>
</head>
<body>
  <div class="cover-page">
    <div style="font-size: 48px; font-weight: bold; margin-bottom: 40px;">LOGO</div>
    <div style="font-size: 32px; font-weight: bold;"><%= proposal.title || '[Proposal Name]' %></div>
    <div style="margin-top: 40px; font-size: 16px;">
      Submitted To: Customer [Agency Name, Office Name]<br>
      Email Address<br><br>
      Submitted By: PBG<br>
      7925 Jones Branch Dr., Ste 2125, McLean, VA 22102 | PBGtech.com<br>
      UEI: T52YD1L9GCA8 | CAGE Code: 65G46 | DUNS Number: 964955558<br>
      Multiple Award Schedule (MAS): GS-35F-706GA<br>
      PBG is an SBA-certified 8(a) and Women-Owned Small Business (WOSB)<br>
      Point of Contact: Ghaleb Ghaleb, CFO | 202.830.8046 | <EMAIL>
    </div>
  </div>
  <div class="header">PBG - <%= proposal.title || 'Project Name' %></div>
  <div class="footer">Use or disclosure of data contained on this sheet is subject to the restriction on the title page of this proposal.</div>
  <div class="content">
    <% if (proposal.agents && proposal.agents.length) { %>
      <% proposal.agents.forEach(function(agent, idx) { %>
        <div class="agent-section">
          <div class="agent-title"><%= agent.name %></div>
          <% if (agent.role) { %><div class="agent-role"><%= agent.role %></div><% } %>
          <div class="ql-editor"><%- agent.response || '' %></div>
        </div>
        <% if (idx < proposal.agents.length - 1) { %><hr><% } %>
      <% }); %>
    <% } else { %>
      <div>No agent responses available.</div>
    <% } %>
  </div>
</body>
</html> 