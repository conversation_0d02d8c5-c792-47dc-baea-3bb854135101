// app.js - Main application entry point
import { state, setState, eventBus } from './state.js';
import { renderTemplates, initTemplatesEventListeners } from './templates.js';
import { renderProposals, initProposalsEventListeners } from './proposals.js';
import { renderProposalDetail, initProposalDetailEventListeners } from './templates/proposal-detail.js';
import { initDropdowns } from './ui/dropdown.js';

const API_BASE_URL = 'https://pbg-backend-445732206884.us-central1.run.app';

async function fetchTemplates() {
  try {
    const response = await fetch(`${API_BASE_URL}/api/templates`);
    if (!response.ok) {
      throw new Error('Failed to fetch templates');
    }
    const templates = await response.json();
    setState({ templateCategories: templates });
  } catch (error) {
    console.error('Error fetching templates:', error);
  }
}

async function fetchProposals(page = state.proposalsPage, pageSize = state.proposalsPageSize) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/proposals?page=${page}&pageSize=${pageSize}`);
    if (!response.ok) {
      throw new Error('Failed to fetch proposals');
    }
    const data = await response.json();
    // Expecting { proposals, total }
    setState({ 
      proposals: data.proposals || [], 
      proposalsTotal: data.total || 0,
      proposalsPage: page,
      proposalsPageSize: pageSize
    });
  } catch (error) {
    console.error('Error fetching proposals:', error);
  }
}

export { fetchProposals };

async function initializeUI() {
  // Fetch initial data
  await fetchTemplates();
  await fetchProposals(state.proposalsPage, state.proposalsPageSize);
  
  // Initial render based on current view
  renderCurrentView();
  
  // Initialize dropdowns after content is rendered
  requestAnimationFrame(() => {
    initDropdowns();
  });
}

function renderCurrentView() {
  const proposalsCol = document.getElementById('proposalsCol');

  // Ensure the proposals column has the correct structure
  if (proposalsCol && state.currentView === 'list' && !proposalsCol.querySelector('.section-header')) {
    proposalsCol.innerHTML = `
      <div class="section-header">
        <div class="section-title">
          <span>Proposals</span>
        </div>
        <div class="section-actions">
          <div class="dropdown">
            <button class="btn btn-ghost dropdown-trigger" id="exportAllBtn" aria-haspopup="true" aria-expanded="false" aria-controls="exportDropdown">
              <i class="fa fa-download"></i>
              Export All (${state.proposals ? state.proposals.length : 0})
              <i class="fa fa-caret-down"></i>
            </button>
            <div class="dropdown-content" id="exportDropdown" role="menu" aria-labelledby="exportAllBtn">
              <a href="#" role="menuitem" id="exportCSV" class="dropdown-item">Export as CSV</a>
              <a href="#" role="menuitem" id="exportPDF" class="dropdown-item">Export as PDF</a>
              <a href="#" role="menuitem" id="exportJSON" class="dropdown-item">Export as JSON</a>
            </div>
          </div>
          <button class="btn btn-accent" id="addProposalBtn">
            <i class="fa fa-plus"></i>
            Add New
          </button>
        </div>
      </div>
      <div class="proposals-content"></div>
    `;
  }

  // Render appropriate view
  if (state.currentView === 'list') {
    // Only clear the content area, not the entire column
    const proposalsContent = proposalsCol.querySelector('.proposals-content');
    if (proposalsContent) {
      proposalsContent.innerHTML = '';
    }
    
    renderTemplates();
    renderProposals();
    // Re-initialize event listeners for list view
    initTemplatesEventListeners();
    initProposalsEventListeners();
  } else if (state.currentView === 'proposal-detail' && state.selectedProposal) {
    proposalsCol.innerHTML = renderProposalDetail(state.selectedProposal);
    initProposalDetailEventListeners(state.selectedProposal);
    requestAnimationFrame(() => {
      initDropdowns();
    });
  }
}

function setupEventListeners() {
  // Listen for state changes
  eventBus.addEventListener('stateChange', (e) => {
    // Re-render the entire view when view changes
    if (e.detail.hasOwnProperty('currentView')) {
      renderCurrentView();
    }
    // Handle other state changes that don't require full view re-render
    else if (e.detail.hasOwnProperty('proposals') || 
             e.detail.hasOwnProperty('templateCategories')) {
      if (state.currentView === 'list') {
        renderTemplates();
        renderProposals();
      }
    }
    
    // Re-initialize dropdowns after any state change that affects the UI
    requestAnimationFrame(() => {
      initDropdowns();
    });
  });
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
  setupEventListeners();
  initializeUI();
}); 