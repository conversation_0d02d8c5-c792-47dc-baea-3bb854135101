<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PBG Login</title>
    <style>
        /* ... existing CSS from login.html ... */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }
        body {
            background-color: #121212;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: white;
        }
        .login-container {
            background-color: #1e1e1e;
            padding: 2.5rem;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 450px;
            border: 1px solid #333;
        }
        .logo {
            text-align: center;
            margin-bottom: 2.5rem;
        }
        .logo h1 {
            color: white;
            font-size: 2.5rem;
            letter-spacing: 1px;
        }
        .logo span {
            color: #4285F4;
        }
        .subtitle {
            text-align: center;
            margin-bottom: 2rem;
            color: #aaa;
        }
        .google-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            color: #444;
            border: none;
            border-radius: 4px;
            width: 100%;
            padding: 0.8rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            margin-bottom: 1.5rem;
            transition: background-color 0.3s;
        }
        .google-btn:hover {
            background-color: #f1f1f1;
        }
        .google-icon {
            margin-right: 1rem;
            width: 20px;
            height: 20px;
        }
        .divider {
            display: flex;
            align-items: center;
            text-align: center;
            margin: 1.5rem 0;
            color: #777;
        }
        .divider::before, .divider::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid #444;
        }
        .divider::before {
            margin-right: .5rem;
        }
        .divider::after {
            margin-left: .5rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #ddd;
        }
        .form-group input {
            width: 100%;
            padding: 0.85rem;
            border: 1px solid #444;
            border-radius: 4px;
            font-size: 1rem;
            background-color: #2d2d2d;
            color: white;
            transition: all 0.3s;
        }
        .form-group input:focus {
            outline: none;
            border-color: #4285F4;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        .login-btn {
            background-color: #4285F4;
            color: white;
            border: none;
            padding: 0.85rem;
            border-radius: 4px;
            width: 100%;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .login-btn:hover {
            background-color: #3367d6;
        }
        .error-message {
            color: #ff6b6b;
            margin-top: 1rem;
            text-align: center;
            padding: 0.7rem;
            background-color: rgba(255, 107, 107, 0.1);
            border-radius: 4px;
        }
        .domain-note {
            font-size: 0.9rem;
            color: #777;
            text-align: center;
            margin-top: 1.5rem;
        }
        .domain-note span {
            color: #4285F4;
        }
        .error-popup {
            position: fixed;
            top: 32px;
            left: 50%;
            transform: translateX(-50%);
            background: #ff6b6b;
            color: #fff;
            padding: 1rem 2rem;
            border-radius: 6px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.18);
            z-index: 1000;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            min-width: 280px;
            max-width: 90vw;
            justify-content: space-between;
            animation: fadeInDown 0.4s;
        }
        .error-popup .close-btn {
            background: none;
            border: none;
            color: #fff;
            font-size: 1.3rem;
            margin-left: 1.5rem;
            cursor: pointer;
        }
        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px) translateX(-50%); }
            to { opacity: 1; transform: translateY(0) translateX(-50%); }
        }
    </style>
</head>
<body>
    <% if (errorMessage) { %>
        <div class="error-popup" id="errorPopup">
            <span><%= errorMessage %></span>
            <button class="close-btn" onclick="document.getElementById('errorPopup').style.display='none'">&times;</button>
        </div>
    <% } %>
    <div class="login-container">
        <div class="logo">
            <h1><span>PBG</span></h1>
        </div>
        <p class="subtitle">Proposals Dashboard</p>
        
        <a href="/auth/azuread" id="microsoftLoginBtn" class="google-btn" style="background:#2F2F2F;color:#fff;">
            <svg width="20" height="20" viewBox="0 0 24 24" style="margin-right:1rem;">
                <rect fill="#F35325" x="2" y="2" width="9" height="9"/>
                <rect fill="#81BC06" x="13" y="2" width="9" height="9"/>
                <rect fill="#05A6F0" x="2" y="13" width="9" height="9"/>
                <rect fill="#FFBA08" x="13" y="13" width="9" height="9"/>
            </svg>
            Sign in with Microsoft
        </a>
        
        <% if (errorMessage) { %>
            <p id="errorMessage" class="error-message"><%= errorMessage %></p>
        <% } %>
        
        <p class="domain-note">Only <span>@pbgtech.com</span> email addresses are permitted</p>
    </div>
</body>
</html> 