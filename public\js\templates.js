// templates.js - CRUD for template categories and agents.
import { state, setState, eventBus } from './state.js';
import { modal } from './ui/modal.js';
import { categoryFormTemplate } from './templates/modal-forms.js';
import { renderTemplateDetail, initTemplateDetailEventListeners } from './templates/template-detail.js';

const templatesCol = document.getElementById('templatesCol');
let isAddingCategory = false;

const API_BASE_URL = 'https://pbg-backend-445732206884.us-central1.run.app';

function handleAddCategory(formData) {
  const newCategory = {
    id: Date.now().toString(),
    name: formData.get('categoryName'),
    description: formData.get('categoryDesc'),
    agents: [],
    createdAt: new Date().toISOString()
  };

  const updatedCategories = [...state.templateCategories, newCategory];
  setState({ templateCategories: updatedCategories });
  hideAddCategoryForm();
}

function showAddCategoryForm() {
  isAddingCategory = true;
  renderTemplates();
}

function hideAddCategoryForm() {
  isAddingCategory = false;
  renderTemplates();
}

function handleEditCategory(categoryId, formData) {
  const index = state.templateCategories.findIndex(cat => cat.id === categoryId);
  if (index !== -1) {
    const updatedCategory = {
      ...state.templateCategories[index],
      name: formData.get('categoryName'),
      description: formData.get('categoryDesc'),
      updatedAt: new Date().toISOString()
    };
    
    state.templateCategories[index] = updatedCategory;
    setState({ templateCategories: state.templateCategories });
  }
}

export async function renderTemplates() {
  const categories = state.templateCategories;
  const templatesContent = templatesCol.querySelector('.templates-content');
  if (!templatesContent) return;

  // If we're in detail view, render that instead
  if (state.currentView === 'category-detail' && state.selectedCategory) {
    templatesContent.innerHTML = renderTemplateDetail(state.selectedCategory);
    initTemplateDetailEventListeners(state.selectedCategory);
    return;
  }

  // Fetch agent lists for all categories in parallel
  const agentLists = await Promise.all(categories.map(async category => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/templates/${category.id}/agents`);
      const agents = response.ok ? await response.json() : [];
      return { ...category, agents };
    } catch (err) {
      return { ...category, agents: [] };
    }
  }));

  let html = isAddingCategory ? categoryFormTemplate : '';
  html += agentLists.map(category => `
    <div class="cat-card" tabindex="0" data-category-id="${category.id}">
      <div class="cat-card__header">
        <div class="cat-card__title">${category.name}</div>
        <div class="cat-card__icons">
          <button aria-label="Edit category" data-action="edit-category" data-category-id="${category.id}"><i class="fa fa-users"></i></button>
          <button aria-label="Delete category" data-action="delete-category" data-category-id="${category.id}">
            <i class="fa fa-trash"></i>
          </button>
        </div>
      </div>
      <div class="cat-card__desc">${category.description}</div>
      <div class="cat-card__badges">
        <span class="badge">${category.agents ? category.agents.length : 0} agents</span>
        <span class="badge">${category.agents ? category.agents.filter(agent => agent.isActive).length : 0} active</span>
      </div>
    </div>
  `).join('');

  templatesContent.innerHTML = html;

  // Set up form event listeners if the form is shown
  if (isAddingCategory) {
    const form = document.getElementById('categoryForm');
    if (form) {
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        if (form.checkValidity()) {
          const formData = new FormData(form);
          handleAddCategory(formData);
        } else {
          form.reportValidity();
        }
      });

      const cancelBtn = form.querySelector('.cat-form__cancel');
      if (cancelBtn) {
        cancelBtn.addEventListener('click', hideAddCategoryForm);
      }
    }
  }
}

export function initTemplatesEventListeners() {
  const addCategoryBtn = templatesCol.querySelector('#addCatBtn');
  if (addCategoryBtn) {
    addCategoryBtn.addEventListener('click', showAddCategoryForm);
  }

  templatesCol.addEventListener('click', async (e) => {
    // Handle button clicks
    const button = e.target.closest('button[data-action]');
    if (button) {
      const action = button.dataset.action;
      const categoryId = button.dataset.categoryId;
      if (action === 'edit-category') {
        const category = state.templateCategories.find(cat => cat.id === categoryId);
        if (category) {
        }
      } else if (action === 'delete-category') {
        if (confirm(`Are you sure you want to delete this category?`)) {
          try {
            const response = await fetch(`${API_BASE_URL}/api/templates/${categoryId}`, {
              method: 'DELETE'
            });
            if (!response.ok) {
              const error = await response.json();
              throw new Error(error.error || 'Failed to delete category');
            }
            // Fetch updated categories from backend
            const categoriesResponse = await fetch(`${API_BASE_URL}/api/templates`);
            const categories = categoriesResponse.ok ? await categoriesResponse.json() : [];
            setState({ templateCategories: categories });
          } catch (err) {
            alert('Failed to delete category: ' + err.message);
          }
        }
      }
      return;
    }

    // Handle card clicks
    const card = e.target.closest('.cat-card');
    if (card && !e.target.closest('button')) {
      const categoryId = card.dataset.categoryId;
      const category = state.templateCategories.find(cat => cat.id === categoryId);
      if (category) {
        // Fetch agents from API
        try {
          const response = await fetch(`${API_BASE_URL}/api/templates/${categoryId}/agents`);
          const agents = response.ok ? await response.json() : [];
          const updatedCategory = { ...category, agents };
          setState({ selectedCategory: updatedCategory, currentView: 'category-detail' });
        } catch (err) {
          console.error('Failed to fetch agents:', err);
          setState({ selectedCategory: category, currentView: 'category-detail' });
        }
      }
    }
  });
}

export function addCategory(category) {
  state.templates.push(category);
  setState({ templates: state.templates });
}

export function updateCategory(index, category) {
  state.templates[index] = category;
  setState({ templates: state.templates });
}

export function deleteCategory(index) {
  state.templates.splice(index, 1);
  setState({ templates: state.templates });
} 