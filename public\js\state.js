// state.js - Global state management
import { renderTemplates } from './templates.js';

const state = {
  templateCategories: [], // Will be loaded from API
  proposals: [], // Will be loaded from API
  selectedCategory: null,
  selectedProposal: null,
  currentView: 'list', // 'list', 'category-detail', 'proposal-detail'
  proposalsPage: 1, // Pagination: current page
  proposalsPageSize: 5, // Pagination: proposals per page (changed from 10 to 5)
  proposalsTotal: 0 // Pagination: total proposals count
};

// Event bus for state changes
const eventBus = document.createElement('div');

// Update state and emit change event
function setState(updates) {
  // Handle view transitions
  if (updates.currentView === 'list') {
    // Clear selections when going back to list view
    updates.selectedProposal = null;
    updates.selectedCategory = null;
  }

  // Apply updates
  Object.assign(state, updates);

  // Emit state change event
  eventBus.dispatchEvent(new CustomEvent('stateChange', { detail: updates }));
}

eventBus.addEventListener('stateChange', () => {
  renderTemplates();
});

export { state, setState, eventBus }; 