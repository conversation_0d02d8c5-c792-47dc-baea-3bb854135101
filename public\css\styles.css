/* Base theme variables */
:root {
  /* Global colors from React globals.css, converted to standard CSS colors */
  --background: #ffffff;
  --foreground: #252525; /* Text color, roughly oklch(0.145 0 0) */
  --card: #ffffff;
  --card-foreground: #252525;
  --popover: #ffffff;
  --popover-foreground: #252525;
  --primary: #030213; /* Primary dark color */
  --primary-foreground: #ffffff; /* Text on primary */
  --secondary: #ececf0; /* For tags, etc. */
  --secondary-foreground: #030213;
  --muted: #ececf0; /* Muted backgrounds */
  --muted-foreground: #717182; /* Muted text */
  --accent: #e9ebef; /* A light accent, usually for backgrounds */
  --accent-foreground: #030213;
  --destructive: #d4183d;
  --destructive-foreground: #ffffff;
  --border: rgba(0, 0, 0, 0.1); /* Light border color */
  --input: transparent;
  --input-background: #f3f3f5;
  --switch-background: #cbced4;
  --ring: #b4b4b4; /* For focus rings */
  --sidebar: #fcfcfd; /* Sidebar background, oklch(0.985 0 0) */
  --sidebar-foreground: #252525;
  --sidebar-primary: #030213;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f2f3f5;
  --sidebar-accent-foreground: #34343a;
  --sidebar-border: #e8e8e8; /* Sidebar border color */
  --sidebar-ring: #b4b4b4;

  /* Radii */
  --radius: 0.625rem; /* 10px - base radius from globals.css */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: 0.75rem; /* 12px - From your initial spec, commonly used for buttons/cards */
  --radius-xl: calc(var(--radius) + 4px);

  /* Font sizes (base is 14px as per globals.css) */
  --font-size: 14px;
  --text-2xl: 1.5rem; /* 24px */
  --text-xl: 1.25rem; /* 20px */
  --text-lg: 1.125rem; /* 18px */
  --text-base: 1rem; /* 16px */
  --text-sm: 0.875rem; /* 14px */
  --text-xs: 0.75rem; /* 12px */

  /* Font weights */
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --font-weight-bold: 700; /* For elements that should be bolder */

  /* Custom spacing and shadow variables from initial spec / screenshot */
  --gap-lg: 24px; /* Used for main column padding */
  --shadow-lg: 0 4px 16px rgba(0,0,0,.08); /* Standard box shadow */
  --shadow-hover: 0 6px 24px rgba(10,90,255,0.10); /* Accentuated hover shadow */
  --accent-button-color: #0A5AFF; /* Specific accent for primary buttons from initial spec */
  --accent-button-hover-color: #0847c7; /* Darker accent for button hover */

  /* Additional Variables */
  --color-input-bg: #fff;
  --color-primary-light: rgba(79, 70, 229, 0.1);
  --color-background-light: #f9fafb;
  --color-background: #fff;
  --color-text: #111827;
  --color-text-light: #6b7280;
  --color-border: #e5e7eb;
  --accent-button-color-light: rgba(10, 90, 255, 0.05);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

/* Base styles */
html {
  font-size: var(--font-size);
}

body {
  font-family: "Inter", sans-serif;
  font-weight: var(--font-weight-normal);
  line-height: 1.45;
  color: var(--foreground);
  background-color: var(--background);
  margin: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

* {
  box-sizing: border-box;
  border-color: var(--border);
  outline-color: var(--ring);
}

/* Typography */
h1 {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
}

h2 {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
}

h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
}

h4 {
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
}

p {
  font-size: var(--text-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.5;
}

label {
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
}

button {
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
}

input {
  font-size: var(--text-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.5;
}

/* Main Grid Layout */
.grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  flex-grow: 1; /* Allow grid to take remaining vertical space */
  width: 100%;
  gap: 1px; /* Thin gap for visual separation */
  overflow: hidden; /* Prevent content overflow from breaking layout */
  background-color: var(--border); /* Background color for the gap */
}

/* Top Bar */
.topbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--gap-lg);
  height: 64px;
  background-color: var(--background);
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 10;
}
.topbar__left {
  display: flex;
  align-items: center;
  gap: 12px;
}
.topbar__icon {
  font-size: var(--text-lg);
  color: var(--muted-foreground);
}
.topbar__title {
  font-size: var(--text-xl); /* Larger font size for title */
  font-weight: var(--font-weight-bold); /* Bold as per screenshot */
  color: var(--foreground);
}
.topbar__right {
  display: flex;
  align-items: center;
  gap: 16px;
}
.topbar__label {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-bold);
  color: var(--foreground);
}

.topbar__proposals-section {
  display: flex;
  align-items: center;
  gap: 16px;
  border-left: 1px solid var(--border);
  padding-left: 32px;
  height: 100%;
  margin-left: 24px;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn i {
  font-size: 14px;
}

.btn-primary {
  background: #000;
  color: white;
}

.btn-primary:hover {
  background: #111;
}

.btn-ghost {
  background: transparent;
  color: var(--color-text);
}

.btn-ghost:hover {
  background: var(--color-background-light);
}

.btn-accent {
  background: var(--accent-button-color);
  color: white;
}

.btn-accent:hover {
  background: var(--accent-button-hover-color);
}

/* Columns */
#templatesCol {
  background-color: var(--sidebar); /* Light gray/off-white for template column */
  padding: var(--gap-lg);
  border-right: 1px solid var(--sidebar-border); /* Subtle vertical divider */
  overflow-y: auto; /* Enable scrolling for content within the column */
  height: 100%; /* Ensure column takes full height */
}

#proposalsCol {
  background-color: var(--background); /* Pure white for proposals column */
  padding: var(--gap-lg);
  overflow-y: auto; /* Enable scrolling for content within the column */
  height: 100%; /* Ensure column takes full height */
}

/* Cards */
.cat-card, .proposal-card {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--text-base); /* Adjusted padding to be consistent */
  margin-bottom: var(--text-base); /* Adjusted margin to be consistent */
  box-shadow: none; /* Removed default box-shadow for cards */
  display: flex;
  flex-direction: column;
  position: relative; /* For positioning inner elements */
  cursor: pointer; /* Indicate interactivity */
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out, transform 0.2s ease-in-out;
}

.cat-card:last-child, .proposal-card:last-child {
  margin-bottom: 0; /* No margin after the last card */
}

.cat-card:focus, .proposal-card:focus, .cat-card:hover, .proposal-card:hover {
  border-color: var(--ring); /* Highlight border on hover/focus */
  box-shadow: var(--shadow-hover); /* Apply hover shadow from variables */
  outline: none; /* Remove default outline for better focus styling */
  transform: translateY(-2px); /* Slight lift effect */
}

.cat-card__header, .proposal-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* Align items to the top for multi-line titles */
  margin-bottom: 0.5rem; /* Space between header and description/badges */
}

.cat-card__title, .proposal-card__title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);
  color: var(--foreground);
  margin-bottom: 0.25rem; /* Space below title */
}

.cat-card__icons {
  display: flex;
  gap: 0.5rem;
  font-size: var(--text-base); /* Adjusted icon size */
  color: var(--muted-foreground);
}

.cat-card__icons button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  font-size: inherit; /* Inherit font size from parent */
  color: inherit; /* Inherit color from parent */
  transition: color 0.2s ease-in-out;
}

.cat-card__icons button:hover, .cat-card__icons button:focus {
  color: var(--primary); /* Darker on hover */
  outline: none;
}

.cat-card__desc {
  font-size: var(--text-sm);
  color: var(--muted-foreground);
  margin-bottom: 0.75rem; /* Space before badges */
}

.cat-card__badges {
  display: flex;
  gap: 0.5rem;
  margin-top: auto; /* Push badges to the bottom */
}

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem; /* Adjusted padding */
  border-radius: var(--radius-lg); /* More rounded corners */
  font-size: var(--text-xs); /* Smaller font size for badges */
  font-weight: var(--font-weight-medium);
  background-color: var(--secondary);
  color: var(--secondary-foreground);
  text-transform: capitalize; /* Ensure consistent capitalization */
}

/* Proposal Card Specific Styles */
.proposal-card__agents {
  font-size: var(--text-sm);
  color: var(--muted-foreground);
  text-align: right; /* Align to the right as per screenshot */
  margin-left: auto; /* Push to the right */
}

.proposal-card__meta {
  display: flex;
  align-items: center;
  gap: 1rem; /* Space between meta items */
  font-size: var(--text-sm);
  color: var(--muted-foreground);
  margin-top: 0.5rem; /* Space below title/description */
}

.proposal-card__meta span {
  display: flex;
  align-items: center;
  gap: 0.25rem; /* Space between icon and text */
}

.proposal-card__meta span i {
  font-size: var(--text-base); /* Icons slightly larger than text */
}

.proposal-card__tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-lg);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  background-color: var(--accent); /* Different background for tags */
  color: var(--accent-foreground);
  text-transform: capitalize;
  margin-left: auto; /* Push tag to the right */
}

.proposal-card__progress {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--accent-button-color); /* Highlight color for progress */
  margin-top: 0.75rem; /* Space above progress */
}

/* Accessibility: Focus styles for interactive elements */
a:focus, button:focus, input:focus, select:focus, textarea:focus, .cat-card:focus, .proposal-card:focus {
  outline: 2px solid var(--ring); /* Visible outline for keyboard navigation */
  outline-offset: 2px;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  margin-bottom: 16px;
  border-bottom: 1px solid var(--color-border);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
}

.section-icon {
  color: var(--color-text-light);
}

.section-actions {
  display: flex;
  gap: 12px;
}

/* Dropdown */
.dropdown {
  position: relative;
  display: inline-block;
  z-index: 100;
}

.dropdown-content {
  display: none;
  position: absolute;
  top: calc(100% + 4px);
  right: 0;
  min-width: 160px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.dropdown.active .dropdown-content {
  display: block;
}

.dropdown-content a {
  color: var(--color-text);
  padding: 8px 16px;
  text-decoration: none;
  display: block;
  font-size: 14px;
  transition: background-color 0.2s;
}

.dropdown-content a:hover {
  background-color: var(--color-background-light);
}

.dropdown-content a:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.dropdown-content a:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

/* Add Category Form */
.cat-form {
  background: var(--color-background);
  border: 2px dashed #e5e7eb;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
}

.cat-form__title {
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0 0 32px 0;
}

.cat-form__group {
  margin-bottom: 24px;
}

.cat-form__label {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text);
  margin-bottom: 8px;
}

.cat-form__input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: var(--color-background);
  font-size: 16px;
  color: #6b7280;
}

.cat-form__input::placeholder {
  color: #9ca3af;
}

.cat-form__textarea {
  min-height: 120px;
  resize: vertical;
}

.cat-form__actions {
  display: flex;
  gap: 12px;
  margin-top: 32px;
}

.cat-form__submit {
  padding: 10px 20px;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cat-form__submit:hover {
  background: #4b5563;
}

.cat-form__cancel {
  padding: 10px 20px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: var(--color-text);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.cat-form__cancel:hover {
  background: #f3f4f6;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: transparent;
  visibility: hidden;
  opacity: 0;
  transition: all 0.2s ease;
  z-index: 9999;
}

.modal--visible {
  visibility: visible;
  opacity: 1;
}

.modal__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: -1;
}

.modal__container {
  background: var(--color-background);
  padding: 1.5rem;
  max-width: 600px;
  max-height: 90vh;
  border-radius: 8px;
  overflow-y: auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-width: 80%;
}

.modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.modal__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text);
  margin: 0;
}

.modal__close {
  background: transparent;
  border: none;
  padding: 0.5rem;
  margin: -0.5rem;
  cursor: pointer;
  color: var(--color-text-light);
}

.modal__close:hover {
  color: var(--color-text);
}

.modal__content {
  margin-bottom: 1.5rem;
}

.modal__footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Form Elements */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text);
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-background);
  font-size: 0.875rem;
  color: var(--color-text);
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px var(--ring-light);
}

.form-textarea {
  width: 100%;
  min-height: 120px;
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-background);
  font-size: 0.875rem;
  color: var(--color-text);
  resize: vertical;
  transition: border-color 0.2s;
}

.form-textarea:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px var(--ring-light);
}

.form-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.form-checkbox input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  border: 1px solid var(--color-border);
  border-radius: 0.25rem;
  background: var(--color-background);
  cursor: pointer;
}

.form-checkbox input[type="checkbox"]:checked {
  background-color: var(--accent-button-color);
  border-color: var(--accent-button-color);
}

.form-checkbox label {
  font-size: 0.875rem;
  color: var(--color-text);
  cursor: pointer;
}

/* Dropzone */
.dropzone {
  border: 2px dashed var(--color-border);
  border-radius: 4px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dropzone--active {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.dropzone__prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-light);
}

.dropzone__prompt i {
  font-size: 2rem;
}

.dropzone__input {
  display: none;
}

.dropzone__preview {
  margin-top: 1rem;
}

.dropzone__file {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--color-background-light);
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.dropzone__file i {
  color: var(--color-text-light);
}

.dropzone__remove {
  margin-left: auto;
  background: none;
  border: none;
  color: var(--color-text-light);
  cursor: pointer;
  padding: 0.25rem;
}

.dropzone__remove:hover {
  color: var(--color-text);
}

/* Template Detail View */
.template-detail {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.template-detail__header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-background);
}

.template-detail__title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  font-size: 1.25rem;
  font-weight: 500;
}

.template-detail__desc {
  color: var(--color-text-muted);
  margin-top: 0.25rem;
  font-size: 0.875rem;
}

.template-detail__content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.template-detail__agents {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

/* Agent Card */
.agent-card {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
}

.agent-card__order {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-muted);
}

.agent-card__order .fa-grip-vertical {
  cursor: move;
}

.agent-card__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.agent-card__field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.agent-card__field label {
  font-size: 0.875rem;
  color: var(--color-text-muted);
}

.agent-card__settings {
  display: flex;
  gap: 2rem;
}

.agent-card__switch {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.agent-card__switch input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  margin: 0;
  border: 1px solid var(--color-border);
  border-radius: 0.25rem;
  background: var(--color-background);
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  position: relative;
  vertical-align: middle;
}

.agent-card__switch input[type="checkbox"]:checked {
  background-color: var(--accent-button-color);
  border-color: var(--accent-button-color);
}

.agent-card__switch input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  left: 4px;
  top: 1px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.agent-card__switch input[type="checkbox"]:focus {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

.agent-card__switch label {
  font-size: 0.875rem;
  color: var(--color-text);
  cursor: pointer;
}

.agent-card__prompt {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.agent-card__prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agent-card__prompt-header label {
  font-size: 0.875rem;
  color: var(--color-text-muted);
}

.agent-card__prompt-preview {
  background: var(--color-background-light);
  border: 1px solid var(--color-border);
  border-radius: 0.375rem;
  padding: 1rem;
}

.prompt-preview__label {
  font-size: 0.75rem;
  color: var(--color-text-muted);
  margin-bottom: 0.5rem;
}

.prompt-preview__content {
  font-family: monospace;
  font-size: 0.875rem;
  white-space: pre-wrap;
  max-height: 8rem;
  overflow: hidden;
  width: 100%;
}

.prompt-preview__stats {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--color-text-muted);
}

.agent-card__actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Prompt Editor Modal */
.prompt-editor {
  margin: -1rem;
}

.prompt-editor .form-textarea {
  width: 100%;
  min-height: 300px;
  font-family: monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  padding: 1rem;
  border: none;
  resize: vertical;
  background: var(--color-background-light);
}

/* Add Agent Button */
#addAgentBtn {
  width: 100%;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

/* Proposal Detail View */
.proposal-detail {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--background);
  z-index: 100;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.proposal-detail__header {
  padding: 24px 7px;
  border-bottom: 1px solid var(--border);
  background: var(--background);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.proposal-detail__nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.proposal-detail__title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-direction: column;
}

.proposal-detail__title {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--foreground);
  margin: 0;
}

.proposal-detail__meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--muted-foreground);
  font-size: var(--text-sm);
  padding-top: 18px;
}

.proposal-detail__tag {
  background: var(--accent);
  color: var(--accent-foreground);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-lg);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
}

.proposal-detail__actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  gap: 1rem;
  padding: 18px 0;
}

.proposal-detail__progress {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
}

.progress-bar {
  height: 6px;
  background: var(--secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  width: 100%;
}

.progress-bar__fill {
  height: 100%;
  background: var(--accent-button-color);
  transition: width 0.3s ease;
}

.proposal-detail__documents {
  border-bottom: 1px solid var(--border);
}

.proposal-detail__documents h2{
  padding-left: 24px;
}

.document-list {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
  flex-direction: column;
  max-height: 200px;
  overflow-y: auto;
}

.document-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--secondary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
}

.document-item--main {
  background: var(--accent);
  color: var(--accent-foreground);
}

.document-item__name {
  font-weight: var(--font-weight-medium);
}

.document-item__size {
  color: var(--muted-foreground);
}

.proposal-detail__content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.proposal-detail__sidebar {
  width: 420px;
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-header {
  padding: 0 24px;
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agent-list {
  overflow-y: auto;
  padding: var(--gap-md);
  padding-top: 20px;
  min-height: 250px;
}

.agent-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  margin-bottom: 0.8rem;
  background: var(--card);
  cursor: pointer;
  transition: all 0.2s ease;
}

.agent-card:hover {
  border-color: var(--ring);
  transform: translateY(-1px);
}

.agent-card--confirmed {
  border-color: var(--accent-button-color);
}

.agent-card--edited {
  border-color: var(--warning);
}

.agent-card--viewed {
  border-color: var(--info);
}

.agent-card__icon {
  color: var(--muted-foreground);
}

.agent-card--confirmed .agent-card__icon {
  color: var(--accent-button-color);
}

.agent-card--edited .agent-card__icon {
  color: var(--warning);
}

.agent-card--viewed .agent-card__icon {
  color: var(--info);
}

.agent-card__content {
  flex: 1;
}

.agent-card__name {
  font-weight: var(--font-weight-medium);
  margin-bottom: 0.25rem;
}

.agent-card__status {
  font-size: var(--text-sm);
  color: var(--muted-foreground);
}

.sidebar-footer {
  padding: var(--gap-lg);
  border-top: 1px solid var(--border);
}

.agent-stats {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 1.5rem;
  font-size: var(--text-sm);
  color: var(--muted-foreground);
  flex-wrap: nowrap;
  padding: 0.75rem 0;
}

.agent-stats__item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.agent-stats__label {
  color: var(--muted-foreground);
}

.agent-stats__value {
  font-weight: var(--font-weight-medium);
  color: var(--foreground);
}

.agent-stats__separator {
  width: 1px;
  height: 1.25rem;
  background-color: var(--border);
  flex-shrink: 0;
}

.proposal-detail__main {
  flex: 1;
  overflow-y: auto;
  padding: var(--gap-lg);
}

.agent-detail__header {
  margin-bottom: 2rem;
}

.agent-detail__meta {
  display: flex;
  gap: 1rem;
  color: var(--muted-foreground);
  font-size: var(--text-sm);
  margin: 0.5rem 0 1rem;
}

.agent-detail__actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Switch component */
.switch {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.switch input {
  display: none;
}

.slider {
  position: relative;
  width: 48px;
  height: 24px;
  background: var(--secondary);
  border-radius: 24px;
  transition: 0.3s;
}

.slider:before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: white;
  top: 2px;
  left: 2px;
  transition: 0.3s;
}

.switch input:checked + .slider {
  background: var(--accent-button-color);
}

.switch input:checked + .slider:before {
  transform: translateX(24px);
}

.switch__label {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
}

.agent-detail{
  height: 98%;
  display: flex;
  flex-direction: column;
}

.agent-detail__response {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--gap-lg);
  margin: 1rem 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.response-content {
  margin-top: 1rem;
  font-family: monospace;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.agent-detail__actions-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

/* Proposal Detail Footer */
.proposal-detail__footer {
  border-top: 1px solid var(--border);
  padding: var(--gap-md) var(--gap-lg);
  background: var(--background);
  display: flex;
  justify-content: center;
}

.response-editor {
  width: 100%;
  min-height: 200px;
  padding: 1rem;
  font-family: monospace;
  font-size: var(--text-sm);
  line-height: 1.5;
  border: 1px solid var(--border);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  background: var(--background);
  resize: vertical;
  flex: 1;
}

.response-editor:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px var(--ring-light);
}

.status-select {
  padding: 0.25rem 0.5rem;
  font-size: var(--text-sm);
  border: 1px solid var(--border);
  border-radius: var(--radius-sm);
  background: var(--background);
  color: var(--foreground);
  cursor: pointer;
}

.status-select:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px var(--ring-light);
}

.agent-card--generating {
  border-color: var(--accent-button-color);
  background: var(--accent-button-color-light);
}

.agent-card--generating .agent-card__icon {
  color: var(--accent-button-color);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.fa-spinner {
  animation: spin 1s linear infinite;
}

/* Document interactions */
.document-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.document-item:hover {
  background: var(--accent);
  transform: translateY(-1px);
}

/* Custom agent form */
.form-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.form-checkbox input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  margin: 0;
  cursor: pointer;
}

.form-checkbox span {
  font-size: var(--text-sm);
  color: var(--foreground);
}

/* Prompt editor */
.prompt-editor {
  margin: -1rem;
}

.prompt-editor .form-textarea {
  width: 100%;
  min-height: 300px;
  font-family: monospace;
  font-size: var(--text-sm);
  line-height: 1.5;
  padding: 1rem;
  border: none;
  resize: vertical;
  background: var(--background-light);
}

/* Question mode section */
.question-mode {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--background-light);
  border-radius: var(--radius-lg);
}

.question-mode__title {
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  margin-bottom: 1rem;
}

.question-mode__list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.question-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.question-item__label {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--foreground);
}

.question-item__input {
  width: 100%;
  padding: 0.5rem;
  font-size: var(--text-sm);
  border: 1px solid var(--border);
  border-radius: var(--radius-sm);
  background: var(--background);
}

.agent-card--selected {
  border-color: var(--accent-button-color);
  background-color: var(--accent-button-color-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-hover);
}

.agent-card--selected .agent-card__name {
  color: var(--accent-button-color);
}

/* Disabled state for buttons and inputs */
.btn:disabled,
.form-input:disabled,
.form-textarea:disabled,
.status-select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Form select with description */
.form-select--with-description {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: 1rem;
  line-height: 1.5;
  cursor: pointer;
}

.form-select--with-description option {
  padding: 0.5rem;
}

.form-select--with-description option small {
  display: block;
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}

/* Selected category info */
.selected-category-info {
  margin-top: 0.5rem;
  padding: 1rem;
  background-color: var(--bg-muted);
  border-radius: 0.375rem;
  border: 1px solid var(--border-color);
}

.selected-category-info__name {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.selected-category-info__agents {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-bottom: 0.5rem;
}

.selected-category-info__description {
  font-size: 0.875rem;
  color: var(--text-color);
  line-height: 1.5;
} 

.proposal-card__delete-btn{
  cursor: pointer;
  background-color: transparent;
  border: 0;
}

.agent-card__prompt button{
  width: 100px;
  margin-left: auto;
  justify-content: center;
  text-align: center;
}

.ql-toolbar.ql-snow{
  border-radius: 0.75rem 0.75rem 0 0;
}

/* Make modal Save button black */
.modal__footer .modal__submit {
  background: #000 !important;
  color: #fff !important;
  border: none !important;
}

/* Make checkboxes blue when checked */
input[type="checkbox"]:checked {
  accent-color: #2563eb;
}

/* Custom Agent Form Styles */
#customAgentForm, #editAgentForm {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1.5rem 1rem 1rem 1rem;
  background: var(--background);
  margin: 0 auto;
}
#customAgentForm .form-group, #editAgentForm .form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
#customAgentForm label, #editAgentForm label {
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  color: var(--foreground);
  margin-bottom: 0.25rem;
}
#customAgentForm input[type="text"],
#customAgentForm textarea,
#editAgentForm input[type="text"],
#editAgentForm textarea,
.question-mode-section__input {
  padding: 0.75rem 1rem;
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  color: var(--foreground);
  transition: border-color 0.2s;
}
#customAgentForm input[type="text"]:focus,
#customAgentForm textarea:focus,
#editAgentForm input[type="text"]:focus,
#editAgentForm textarea:focus
.question-mode-section__input:focus {
  border-color: var(--accent-button-color);
  outline: none;
}
#customAgentForm textarea,
#editAgentForm textarea {
  min-height: 240px;
  resize: vertical;
}
#customAgentForm .checkbox-label, #editAgentForm .checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--text-base);
  cursor: pointer;
  user-select: none;
}
#customAgentForm input[type="checkbox"], #editAgentForm input[type="checkbox"] {
  accent-color: var(--accent-button-color);
  width: 1.1em;
  height: 1.1em;
  margin-right: 0.5em;
}

.agent-card__custom-tag {
  display: inline-block;
  font-size: var(--text-xs);
  color: #787a7d;
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-medium);
  vertical-align: middle;
  letter-spacing: 0.02em;
}

.ql-font-roboto { font-family: 'Roboto', sans-serif; }
.ql-font-lato { font-family: 'Lato', sans-serif; }
.ql-font-montserrat { font-family: 'Montserrat', sans-serif; }
.ql-font-georgia { font-family: 'Georgia', serif; }
.ql-font-courier { font-family: 'Courier New', monospace; }
.ql-font-arial { font-family: 'Arial', sans-serif; }
.ql-font-times { font-family: 'Times New Roman', serif; }
.ql-font-sans { font-family: sans-serif; }
.ql-font-serif { font-family: serif; }
.ql-font-monospace { font-family: monospace; }

.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="8px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="8px"]::before { content: "8px"; }
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="10px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="10px"]::before { content: "10px"; }
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="12px"]::before { content: "12px"; }
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="14px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="14px"]::before { content: "14px"; }
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="16px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="16px"]::before { content: "16px"; }
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="18px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="18px"]::before { content: "18px"; }
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="20px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="20px"]::before { content: "20px"; }
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="24px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="24px"]::before { content: "24px"; }
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="28px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="28px"]::before { content: "28px"; }
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="32px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="32px"]::before { content: "32px"; }
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="36px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="36px"]::before { content: "36px"; }
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="48px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="48px"]::before { content: "48px"; }
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="64px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="64px"]::before { content: "64px"; }
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="72px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="72px"]::before { content: "72px"; }

/* Font family labels in Quill picker */
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="sans"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="sans"]::before { content: "Sans Serif"; font-family: sans-serif; }
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before { content: "Serif"; font-family: serif; }
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before { content: "Monospace"; font-family: monospace; }
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="roboto"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="roboto"]::before { content: "Roboto"; font-family: 'Roboto', sans-serif; }
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="lato"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="lato"]::before { content: "Lato"; font-family: 'Lato', sans-serif; }
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="montserrat"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="montserrat"]::before { content: "Montserrat"; font-family: 'Montserrat', sans-serif; }
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="georgia"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="georgia"]::before { content: "Georgia"; font-family: Georgia, serif; }
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="courier"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="courier"]::before { content: "Courier New"; font-family: 'Courier New', monospace; }
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="arial"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="arial"]::before { content: "Arial"; font-family: Arial, sans-serif; }
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="times"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="times"]::before { content: "Times New Roman"; font-family: 'Times New Roman', serif; }

/* Font family application for content */
.ql-font-sans { font-family: sans-serif; }
.ql-font-serif { font-family: serif; }
.ql-font-monospace { font-family: monospace; }
.ql-font-roboto { font-family: 'Roboto', sans-serif; }
.ql-font-lato { font-family: 'Lato', sans-serif; }
.ql-font-montserrat { font-family: 'Montserrat', sans-serif; }
.ql-font-georgia { font-family: Georgia, serif; }
.ql-font-courier { font-family: 'Courier New', monospace; }
.ql-font-arial { font-family: Arial, sans-serif; }
.ql-font-times { font-family: 'Times New Roman', serif; }

#exportAllBtn{
  display: none;
}

.question-mode-section {
  display: none;
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--gap-lg);
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}
.question-mode-section__title {
  margin-bottom: 1em;
  color: #000;
  font-size: 1.2em;
  font-weight: 600;
  margin-top: 0;
}
.question-mode-section__label {
  font-weight: 500;
}
.question-mode-section__input {
  width: 100%;
  min-height: 40px;
  margin-top: 0.5em;
  resize: vertical;
}
.question-mode-section__submit {
  width: 100%;
  margin-top: 1em;
  text-align: center;
  display: block;
}