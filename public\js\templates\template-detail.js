// template-detail.js - Template category detail view functionality
import { state, setState } from '../state.js';
import { modal } from '../ui/modal.js';
import { agentFormTemplate } from './modal-forms.js';

const API_BASE_URL = 'https://pbg-backend-445732206884.us-central1.run.app';

export function renderTemplateDetail(category) {
  return `
    <div class="template-detail" data-cat-id=${category.id}>
      <div class="template-detail__header">
        <button class="btn btn-ghost" id="backToCategories">
          <i class="fa fa-arrow-left"></i>
          Back to Categories
        </button>
        
        <div class="template-detail__title">
          <i class="fa fa-cog"></i>
          <span>${category.name}</span>
          <button class="btn btn-ghost btn-sm" id="editTemplateBtn" title="Edit Template">
            <i class="fa fa-edit"></i>
          </button>
        </div>
        <div class="template-detail__desc">${category.description}</div>
      </div>

      <div class="template-detail__content">
        <div class="template-detail__agents">
          ${(Array.isArray(category.agents) ? category.agents : []).map((agent, index) => `
            <div class="agent-card" data-agent-id="${agent.id}">
              <div class="agent-card__order">
                <i class="fa fa-grip-vertical"></i>
                <span class="badge">${index + 1}</span>
              </div>
              
              <div class="agent-card__content">
                <div class="agent-card__field">
                  <label for="agent-name-${agent.id}">Agent Name</label>
                  <input type="text" id="agent-name-${agent.id}" class="form-input" value="${agent.name}" 
                         data-field="name" data-agent-id="${agent.id}" readonly>
                </div>

                <div class="agent-card__field">
                  <label for="agent-desc-${agent.id}">Description</label>
                  <input type="text" id="agent-desc-${agent.id}" class="form-input" value="${agent.role || ''}" 
                         data-field="description" data-agent-id="${agent.id}" readonly>
                </div>

                <div class="agent-card__settings">
                  <div class="agent-card__switch">
                    <input type="checkbox" id="agent-active-${agent.id}" ${agent.isActive ? 'checked' : ''} 
                           data-field="isActive" data-agent-id="${agent.id}" disabled>
                    <label for="agent-active-${agent.id}">Active</label>
                  </div>

                  <div class="agent-card__switch">
                    <input type="checkbox" id="agent-question-${agent.id}" ${agent.questionMode ? 'checked' : ''} 
                           data-field="questionMode" data-agent-id="${agent.id}" disabled>
                    <label for="agent-question-${agent.id}">Question Mode</label>
                  </div>
                </div>

                <div class="agent-card__prompt">
                  <div class="agent-card__prompt-header">
                    <span class="prompt-preview__label">AI Prompt</span>
                  </div>
                  
                  <div class="agent-card__prompt-preview">
                    <div class="prompt-preview__content" id="prompt-input-${agent.id}" rows="6" style="resize: none;" readonly>${agent.prompt || ''}</div>
                    <div class="prompt-preview__stats">
                      ${agent.prompt ? `${agent.prompt.split('\n').length} lines • ${agent.prompt.length} characters` : 'No prompt set'}
                    </div>
                  </div>

                  <button class="btn btn-primary btn-sm" data-action="edit-agent" data-agent-id="${agent.id}">
                      Edit
                  </button>
                </div>
              </div>

              <div class="agent-card__actions">
                <button class="btn btn-ghost btn-sm" data-action="move-up" data-agent-id="${agent.id}" ${index === 0 ? 'disabled' : ''}>
                  <i class="fa fa-arrow-up"></i>
                </button>
                <button class="btn btn-ghost btn-sm" data-action="move-down" data-agent-id="${agent.id}" ${index === category.agents.length - 1 ? 'disabled' : ''}>
                  <i class="fa fa-arrow-down"></i>
                </button>
                <button class="btn btn-ghost btn-sm text-red-600" data-action="delete-agent" data-agent-id="${agent.id}">
                  <i class="fa fa-trash"></i>
                </button>
              </div>
            </div>
          `).join('')}
        </div>

        <button class="btn btn-accent" id="addAgentBtn">
          <i class="fa fa-plus"></i>
          Add Agent
        </button>
      </div>
    </div>
  `;
}

export function initTemplateDetailEventListeners(category) {
  // Back button
  const backBtn = document.getElementById('backToCategories');
  if (backBtn) {
    backBtn.addEventListener('click', () => {
      setState({ selectedCategory: null, currentView: 'list' });
    });
  }

  // Edit template button
  const editTemplateBtn = document.getElementById('editTemplateBtn');
  if (editTemplateBtn) {
    editTemplateBtn.addEventListener('click', () => {
      modal.open({
        title: 'Edit Template',
        content: `
          <form id="editTemplateForm">
            <div class="form-group">
              <label for="edit-template-name">Template Name</label>
              <input type="text" id="edit-template-name" name="name" class="form-input" value="${category.name}" required>
            </div>
            <div class="form-group">
              <label for="edit-template-desc">Description</label>
              <textarea id="edit-template-desc" name="description" class="form-textarea" rows="3">${category.description || ''}</textarea>
            </div>
          </form>
        `,
        onSubmit: async () => {
          const form = document.getElementById('editTemplateForm');
          if (form && form.checkValidity()) {
            const name = document.getElementById('edit-template-name').value;
            const description = document.getElementById('edit-template-desc').value;
            try {
              const res = await fetch(`${API_BASE_URL}/api/templates/${category.id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ name, description })
              });
              if (!res.ok) {
                const error = await res.json().catch(() => ({}));
                throw new Error(error.error || 'Failed to update template');
              }
              // Update UI with new values
              const updatedCategory = { ...category, name, description };
              setState({ selectedCategory: updatedCategory });
              alert('Template updated successfully!');
              return true; // Close modal
            } catch (err) {
              alert('Failed to update template: ' + err.message);
              return false; // Keep modal open
            }
          } else {
            form && form.reportValidity();
            return false; // Keep modal open
          }
        }
      });
    });
  }

  // Add agent button
  const addAgentBtn = document.getElementById('addAgentBtn');
  if (addAgentBtn) {
    addAgentBtn.addEventListener('click', () => {
      modal.open({
        title: 'Add New Agent',
        content: agentFormTemplate,
        onSubmit: async () => {
          const form = document.getElementById('agentForm');
          if (form && form.checkValidity()) {
            const formData = new FormData(form);
            const agentData = {
              name: formData.get('agentName'),
              role: formData.get('agentDescription'),
              prompt: formData.get('agentPrompt')
            };
            try {
              // Add agent via API
              await import('./modal-forms.js').then(mod => mod.addAgent(category.id, agentData));
              // Re-fetch agents after adding
              const response = await fetch(`${API_BASE_URL}/api/templates/${category.id}/agents`);
              const agents = response.ok ? await response.json() : [];
              const updatedCategory = { ...category, agents };
              setState({ selectedCategory: updatedCategory, currentView: 'category-detail' });
              return true; // Close modal
            } catch (err) {
              console.error('Failed to add or fetch agents:', err);
              return false; // Keep modal open
            }
          } else {
            form && form.reportValidity();
            return false; // Keep modal open
          }
        }
      });
    });
  }

  // Agent card event delegation
  const agentsContainer = document.querySelector('.template-detail__agents');
  if (agentsContainer) {
    agentsContainer.addEventListener('click', (e) => {
      const target = e.target;
      const agentCard = target.closest('.agent-card');
      if (!agentCard) return;

      const agentId = agentCard.dataset.agentId;
      const action = target.closest('[data-action]')?.dataset.action;

      if (action) {
        if (action === 'edit-agent') {
          // Open modal for editing agent
          const agent = category.agents.find(a => a.id === agentId);
          if (agent) {
            modal.open({
              title: `Edit Agent - ${agent.name}`,
              content: `
                <form id="editAgentForm">
                  <div class="form-group">
                    <label for="edit-agent-name">Agent Name</label>
                    <input type="text" id="edit-agent-name" name="name" class="form-input" value="${agent.name}" required>
                  </div>
                  <div class="form-group">
                    <label for="edit-agent-desc">Description</label>
                    <input type="text" id="edit-agent-desc" name="role" class="form-input" value="${agent.role || ''}">
                  </div>
                  <div class="form-group" style="flex-direction: row;">
                    <label><input type="checkbox" id="edit-agent-active" name="isActive" ${agent.isActive ? 'checked' : ''}> Active</label>
                    <label><input type="checkbox" id="edit-agent-question" name="questionMode" ${agent.questionMode ? 'checked' : ''}> Question Mode</label>
                  </div>
                  <div class="form-group">
                    <label for="edit-agent-prompt">AI Prompt</label>
                    <textarea id="edit-agent-prompt" name="prompt" class="form-textarea" rows="6">${agent.prompt || ''}</textarea>
                  </div>
                </form>
              `,
              onSubmit: async () => {
                const form = document.getElementById('editAgentForm');
                if (form && form.checkValidity()) {
                  const name = document.getElementById('edit-agent-name').value;
                  const role = document.getElementById('edit-agent-desc').value;
                  const prompt = document.getElementById('edit-agent-prompt').value;
                  const isActive = document.getElementById('edit-agent-active').checked;
                  const questionMode = document.getElementById('edit-agent-question').checked;
                  try {
                    const res = await fetch(`${API_BASE_URL}/api/templates/${category.id}/agents/${agentId}`, {
                      method: 'PUT',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({ name, role, prompt, isActive, questionMode })
                    });
                    if (!res.ok) {
                      const error = await res.json().catch(() => ({}));
                      throw new Error(error.error || 'Failed to save agent');
                    }
                    // Re-fetch agents to update UI
                    const response = await fetch(`${API_BASE_URL}/api/templates/${category.id}/agents`);
                    const agents = response.ok ? await response.json() : [];
                    const updatedCategory = { ...category, agents };
                    setState({ selectedCategory: updatedCategory, currentView: 'category-detail' });
                    alert('Agent saved successfully!');
                    return true; // Close modal
                  } catch (err) {
                    alert('Failed to save agent: ' + err.message);
                    return false; // Keep modal open
                  }
                } else {
                  form && form.reportValidity();
                  return false; // Keep modal open
                }
              }
            });
          }
          return;
        }
        handleAgentAction(action, agentId, category);
      }
    });

    // Input change handlers
    agentsContainer.addEventListener('change', (e) => {
      const input = e.target;
      const { field, agentId } = input.dataset;
      if (!field || !agentId) return;

      const value = input.type === 'checkbox' ? input.checked : input.value;

      // Update the agent in the category.agents array
      const updatedAgents = category.agents.map(agent =>
        agent.id === agentId ? { ...agent, [field]: value } : agent
      );
      // Update the selectedCategory in state.templateCategories
      const updatedCategory = {
        ...category,
        agents: updatedAgents
      };
      const updatedCategories = state.templateCategories.map(cat =>
        cat.id === category.id ? updatedCategory : cat
      );
      // Update both the state and the local category reference
      setState({ templateCategories: updatedCategories, selectedCategory: updatedCategory });
      // Also update the local category object so further changes are based on the latest state
      category.agents = updatedAgents;
    });
  }
}

function handleAgentAction(action, agentId, category) {
  switch (action) {
    case 'move-up':
    case 'move-down': {
      const currentIndex = category.agents.findIndex(a => a.id === agentId);
      const newIndex = action === 'move-up' ? currentIndex - 1 : currentIndex + 1;
      
      if (newIndex >= 0 && newIndex < category.agents.length) {
        const newAgents = [...category.agents];
        [newAgents[currentIndex], newAgents[newIndex]] = [newAgents[newIndex], newAgents[currentIndex]];
        
        const updatedCategory = {
          ...category,
          agents: newAgents.map((agent, index) => ({ ...agent, order: index + 1 }))
        };

        const updatedCategories = state.templateCategories.map(cat =>
          cat.id === category.id ? updatedCategory : cat
        );

        setState({ templateCategories: updatedCategories });
      }
      break;
    }
    
    case 'delete-agent': {
      if (confirm('Are you sure you want to delete this agent?')) {
        // Call DELETE API
        fetch(`${API_BASE_URL}/api/templates/${category.id}/agents/${agentId}`, {
          method: 'DELETE'
        })
        .then(async (res) => {
          if (!res.ok) throw new Error('Failed to delete agent');
          // Remove agent card from UI immediately
          const agentCard = document.querySelector(`.agent-card[data-agent-id="${agentId}"]`);
          if (agentCard) agentCard.remove();
          // Re-fetch agents after deletion
          const response = await fetch(`${API_BASE_URL}/api/templates/${category.id}/agents`);
          const agents = response.ok ? await response.json() : [];
          const updatedCategory = { ...category, agents };
          setState({ selectedCategory: updatedCategory, currentView: 'category-detail' });
        })
        .catch(err => {
          console.error('Failed to delete agent:', err);
        });
      }
      break;
    }

    case 'edit-prompt': {
      const agent = category.agents.find(a => a.id === agentId);
      if (agent) {
        modal.open({
          title: `Edit AI Prompt - ${agent.name}`,
          content: `
            <div class="prompt-editor">
              <textarea class="form-textarea" id="promptEditor" rows="15">${agent.prompt || ''}</textarea>
            </div>
          `,
          onSubmit: () => {
            const newPrompt = document.getElementById('promptEditor').value;
            
            const updatedCategory = {
              ...category,
              agents: category.agents.map(a =>
                a.id === agentId ? { ...a, prompt: newPrompt } : a
              )
            };

            const updatedCategories = state.templateCategories.map(cat =>
              cat.id === category.id ? updatedCategory : cat
            );

            setState({ templateCategories: updatedCategories });
            return true; // Close modal
          }
        });
      }
      break;
    }
  }
} 