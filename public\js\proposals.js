// proposals.js - CRUD for proposals, docs, and custom agents.
import { state, setState } from './state.js';
import { modal } from './ui/modal.js';
import { proposalFormTemplate, populateCategorySelect, initDropzone } from './templates/modal-forms.js';
import { fetchProposals } from './app.js';

const API_BASE_URL = 'https://pbg-backend-445732206884.us-central1.run.app';
const proposalsCol = document.getElementById('proposalsCol');

// Utility: Global loading overlay
function showLoadingOverlay(message = "Processing...") {
  if (document.getElementById('global-loading-overlay')) return;
  const overlay = document.createElement('div');
  overlay.id = 'global-loading-overlay';
  overlay.style.position = 'fixed';
  overlay.style.top = 0;
  overlay.style.left = 0;
  overlay.style.width = '100vw';
  overlay.style.height = '100vh';
  overlay.style.background = 'rgba(255,255,255,0.7)';
  overlay.style.zIndex = 9999;
  overlay.style.display = 'flex';
  overlay.style.flexDirection = 'column';
  overlay.style.justifyContent = 'center';
  overlay.style.alignItems = 'center';
  overlay.innerHTML = `
    <div style="text-align:center;">
      <i class="fa fa-spinner fa-spin fa-3x"></i>
      <p style="margin-top:1em;font-size:1.2em;">${message}</p>
    </div>
  `;
  document.body.appendChild(overlay);
}

function hideLoadingOverlay() {
  const overlay = document.getElementById('global-loading-overlay');
  if (overlay) overlay.remove();
}

async function handleAddProposal(formData, files) {
  // Get values from formData
  const title = formData.get('proposalTitle');
  const templateId = formData.get('proposalCategory');
  const categorySelect = document.getElementById('proposalCategory');
  const category = categorySelect ? categorySelect.options[categorySelect.selectedIndex].text : '';
  const dueDateRaw = formData.get('proposalDate');
  let dueDate = '';
  if (dueDateRaw) {
    dueDate = new Date(dueDateRaw + 'T00:00:00Z').toISOString();
  }

  // Build FormData for API
  const apiFormData = new FormData();
  apiFormData.append('title', title);
  apiFormData.append('templateId', templateId);
  apiFormData.append('category', category);
  apiFormData.append('dueDate', dueDate);


  if (files && files.length > 0) {
    // Append all files as 'documents' (API expects multiple files under this field)
    files.forEach(file => {
      apiFormData.append('documents', file);
    });
  }

  // Show loading overlay (instead of modal)
  showLoadingOverlay('Analysing document(s)...');

  try {
    // Create the proposal
    const response = await fetch(`${API_BASE_URL}/api/proposals`, {
      method: 'POST',
      body: apiFormData
    });

    if (!response.ok) {
      const error = await response.json();
      hideLoadingOverlay();
      throw new Error(error.error || 'Failed to create proposal');
    }

    const proposal = await response.json();

    hideLoadingOverlay();
    // Fetch updated proposals from backend
    const proposalsResponse = await fetch(`${API_BASE_URL}/api/proposals`);
    const proposals = proposalsResponse.ok ? await proposalsResponse.json() : [];
    setState({ proposals });
    renderProposals();

  } catch (error) {
    hideLoadingOverlay();
    console.error('Error creating proposal:', error);
    modal.open({
      title: 'Error',
      content: `
        <div class="error-message">
          <i class="fa fa-exclamation-circle"></i>
          <p>${error.message}</p>
        </div>
      `,
      submitText: 'OK'
    });
  }
}

function getProposalProgress(proposal) {
  if (!Array.isArray(proposal.agentResponses)) {
    return { confirmed: 0, total: 0 };
  }
  const confirmed = proposal.agentResponses.filter(response => 
    response.status === 'confirmed'
  ).length;
  return { confirmed, total: proposal.agentResponses.length };
}

function getStatusColor(confirmedCount, totalCount) {
  if (confirmedCount === totalCount && totalCount > 0) return 'default';
  if (confirmedCount > 0) return 'secondary';
  return 'outline';
}

export function renderProposals() {
  const proposalsContent = document.querySelector('.proposals-content');
  if (!proposalsContent) return;

  const proposals = state.proposals || [];
  const page = state.proposalsPage || 1;
  const pageSize = state.proposalsPageSize || 10;
  const total = state.proposalsTotal || 0;
  const hasNextPage = (page * pageSize) < total;
  const totalPages = Math.ceil(total / pageSize) || 1;
  const hasPrevPage = page > 1;

  if (proposals.length === 0) {
    proposalsContent.innerHTML = `
      <div class="empty-state">
        <i class="fa fa-file-text-o"></i>
        <h3>No proposals yet</h3>
        <p>Create your first proposal by clicking the "Add New" button above.</p>
      </div>
    `;
    return;
  }

  proposalsContent.innerHTML = proposals.map(proposal => {
    const progress = getProposalProgress(proposal);
    const statusBadgeClass = getStatusColor(progress.confirmed, progress.total);
    const category = state.templateCategories.find(cat => cat.id === proposal.templateCategory);
    // Format due date as 'YYYY-MM-DDTHH:mm:ssZ'
    let dueDateISO = '';
    if (proposal.dueDate) {
      // If already in ISO format, use as is, else convert
      if (typeof proposal.dueDate === 'string' && proposal.dueDate.includes('T')) {
        dueDateISO = proposal.dueDate;
      } else {
        dueDateISO = new Date(proposal.dueDate + 'T00:00:00Z').toISOString();
      }
    }
    return `
      <div class="proposal-card" data-proposal-id="${proposal.id}">
        <div class="proposal-card__header">
          <div class="proposal-card__title">${proposal.title}</div>
          <div class="proposal-card__category">${proposal.category}</div>
          <button class="proposal-card__delete-btn" title="Delete Proposal" data-delete-proposal-id="${proposal.id}">
            <i class="fa fa-trash"></i>
          </button>
        </div>
        <div class="proposal-card__meta">
          <span class="proposal-card__date">Due: ${dueDateISO}</span>
          <span class="proposal-card__docs">${proposal.documents ? proposal.documents.length : 0} document(s)</span>
          <span class="proposal-status-indicator" data-proposal-id="${proposal.id}">Loading status...</span>
        </div>
      </div>
    `;
  }).join('');

  // Pagination controls
  proposalsContent.innerHTML += `
    <style>
      #gotoPageInput::-webkit-outer-spin-button,
      #gotoPageInput::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
      #gotoPageInput {
        -moz-appearance: textfield;
      }
    </style>
    <div class="pagination-controls" style="text-align:center;margin:2em 0 1em 0;display:flex;justify-content:center;align-items:center;gap:1em;">
      <button id="prevPageBtn" class="btn btn-secondary" ${hasPrevPage ? '' : 'disabled'}>Previous Page</button>
      <span>Page ${page} of ${totalPages}</span>
      <input id="gotoPageInput" type="number" min="1" max="${totalPages}" value="${page}" style="width:3em;padding:2px 4px;font-size:1em;" title="Go to page" />
      <button id="gotoPageBtn" class="btn btn-secondary">Go</button>
      <button id="nextPageBtn" class="btn btn-secondary" ${hasNextPage ? '' : 'disabled'}>Next Page</button>
    </div>
  `;

  // Pagination button events
  const prevPageBtn = document.getElementById('prevPageBtn');
  if (prevPageBtn && hasPrevPage) {
    prevPageBtn.onclick = async () => {
      await fetchProposals(page - 1, pageSize);
    };
  }
  const nextPageBtn = document.getElementById('nextPageBtn');
  if (nextPageBtn && hasNextPage) {
    nextPageBtn.onclick = async () => {
      await fetchProposals(page + 1, pageSize);
    };
  }
  // Go to page input/button events
  const gotoPageInput = document.getElementById('gotoPageInput');
  const gotoPageBtn = document.getElementById('gotoPageBtn');
  function gotoPageHandler() {
    let entered = parseInt(gotoPageInput.value, 10);
    if (isNaN(entered) || entered < 1) entered = 1;
    if (entered > totalPages) entered = totalPages;
    if (entered !== page) {
      fetchProposals(entered, pageSize);
    }
  }
  if (gotoPageBtn) {
    gotoPageBtn.onclick = gotoPageHandler;
  }
  if (gotoPageInput) {
    gotoPageInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        gotoPageHandler();
      }
    });
  }

  // Poll status for each proposal
  proposals.forEach(proposal => {
    const statusSpan = document.querySelector(`.proposal-status-indicator[data-proposal-id="${proposal.id}"]`);
    if (!statusSpan) return;
    // Only check status once
    const checkStatus = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/proposals/${proposal.id}/status`);
        if (response.ok) {
          const statusData = await response.json();
          statusSpan.textContent = `Status: ${statusData.status.charAt(0).toUpperCase() + statusData.status.slice(1)}`;
        } else {
          statusSpan.textContent = 'Status: Unknown';
        }
      } catch (e) {
        statusSpan.textContent = 'Status: Error';
      }
    };
    checkStatus();
  });

  // Add event listener for delete button in proposals-content (only once)
  if (!proposalsContent._deleteListenerAdded) {
    proposalsContent.addEventListener('click', (e) => {
      const deleteBtn = e.target.closest('.proposal-card__delete-btn');
      if (deleteBtn) {
        e.stopPropagation();
        const proposalId = deleteBtn.getAttribute('data-delete-proposal-id');
        if (proposalId) {
          handleDeleteProposal(proposalId);
        }
        return;
      }
      // Existing card click logic
      const card = e.target.closest('.proposal-card');
      if (card && !e.target.closest('.proposal-card__delete-btn')) {
        const proposalId = card.dataset.proposalId;
        const proposal = state.proposals.find(p => p.id === proposalId);
        if (proposal) {
          setState({ selectedProposal: proposal, currentView: 'proposal-detail' });
        }
      }
    });
    proposalsContent._deleteListenerAdded = true;
  }
}

function getCategoryName(categoryId) {
  const category = state.templateCategories.find(cat => cat.id === categoryId);
  return category ? category.name : 'Uncategorized';
}

function formatDate(dateString) {
  // Return ISO string in 'YYYY-MM-DDTHH:mm:ssZ' format
  if (!dateString) return '';
  if (typeof dateString === 'string' && dateString.includes('T')) {
    return dateString;
  }
  return new Date(dateString + 'T00:00:00Z').toISOString();
}

function calculateProgress(proposal) {
  if (!proposal.agentResponses || proposal.agentResponses.length === 0) return 0;
  const completed = proposal.agentResponses.filter(response => response.status === 'confirmed').length;
  return Math.round((completed / proposal.agentResponses.length) * 100);
}

async function deleteProposalFromServer(proposalId) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/proposals/${proposalId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Server response:', errorData);
      throw new Error(errorData.error || 'Failed to delete proposal');
    }

    const result = await response.json();
    return true;
  } catch (error) {
    console.error('Error deleting proposal:', error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

export async function handleDeleteProposal(proposalId) {
  if (!proposalId) {
    console.error('No proposal ID provided');
    return;
  }

  // Show confirmation modal
  modal.open({
    title: 'Delete Proposal',
    content: `
      <div class="confirmation-dialog">
        <p>Are you sure you want to delete this proposal? This action cannot be undone.</p>
      </div>
    `,
    submitText: 'Delete',
    cancelText: 'Cancel',
    onSubmit: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/proposals/${proposalId}`, {
          method: 'DELETE'
        });
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to delete proposal');
        }
        // Fetch updated proposals from backend
        const proposalsResponse = await fetch(`${API_BASE_URL}/api/proposals`);
        const proposals = proposalsResponse.ok ? await proposalsResponse.json() : [];
        setState({ proposals, selectedProposal: null, currentView: 'list' });
        renderProposals();
      } catch (err) {
        alert('Failed to delete proposal: ' + err.message);
      }
      return true; // Close modal
    }
  });
}

function showAddProposalModal() {
  modal.open({
    title: 'Add New Proposal',
    content: proposalFormTemplate,
    onShow: (modalEl) => {
      // Initialize category select
      const categorySelect = modalEl.querySelector('#proposalCategory');
      if (categorySelect) {
        populateCategorySelect(categorySelect);
      }
      // Initialize dropzone
      const dropzoneEl = modalEl.querySelector('#proposalDocs');
      if (dropzoneEl) {
        initDropzone(dropzoneEl);
      }
      // Set minimum date to today for the due date input
      const dateInput = modalEl.querySelector('#proposalDate');
      if (dateInput) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.min = today;
      }
    },
    onSubmit: async (formEl) => {
      // Prevent default form submission
      if (formEl) event.preventDefault && event.preventDefault();
      // Collect form data
      const formData = new FormData(formEl);
      // Collect files from dropzone
      let files = [];
      const dropzone = formEl.querySelector('#proposalDocs');
      if (dropzone && dropzone.droppedFiles && dropzone.droppedFiles.length > 0) {
        files = Array.from(dropzone.droppedFiles);
      } else {
        const dropzoneInput = formEl.querySelector('#proposalDocs input[type="file"]');
        if (dropzoneInput && dropzoneInput.files) {
          files = Array.from(dropzoneInput.files);
        }
      }
      await handleAddProposal(formData, files);
    }
  });
}

export function initProposalsEventListeners() {
  const addProposalBtn = document.getElementById('addProposalBtn');
  if (addProposalBtn) {
    addProposalBtn.addEventListener('click', showAddProposalModal);
  }

  // Handle proposal card clicks
  const proposalsContent = document.querySelector('.proposals-content');
  if (proposalsContent) {
    proposalsContent.addEventListener('click', (e) => {
      const card = e.target.closest('.proposal-card');
      if (card) {
        const proposalId = card.dataset.proposalId;
        const proposal = state.proposals.find(p => p.id === proposalId);
        if (proposal) {
          setState({ selectedProposal: proposal, currentView: 'proposal-detail' });
        }
      }
    });
  }

  // Handle export actions
  const exportDropdown = document.getElementById('exportDropdown');
  if (exportDropdown) {
    exportDropdown.addEventListener('click', (e) => {
      e.preventDefault();
      const exportItem = e.target.closest('.dropdown-item');
      if (!exportItem) return;

      const completedProposals = state.proposals.filter(proposal => {
        const progress = getProposalProgress(proposal);
        return progress.confirmed === progress.total && progress.total > 0;
      });

      if (completedProposals.length === 0) {
        alert('No completed proposals to export');
        return;
      }

      const format = exportItem.dataset.format;
      // TODO: Implement actual export functionality
    });
  }
}